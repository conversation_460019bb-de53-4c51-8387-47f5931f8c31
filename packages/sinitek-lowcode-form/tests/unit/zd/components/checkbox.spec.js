import { shallowMount } from '@vue/test-utils'
import ZDCheckbox from '@/zd/components/checkbox/index.vue'
import { FormState } from '@/zd/common/enum'
import { OtherKey } from '@/zd/common/enum'

describe('ZDCheckbox组件', () => {
  const mockOptions = [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' },
    { label: '其他', value: OtherKey },
  ]

  let wrapper

  beforeEach(() => {
    wrapper = shallowMount(ZDCheckbox, {
      propsData: {
        value: [],
        label: '测试多选框',
        options: mockOptions,
        useColor: false,
        dir: 'row',
      },
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  it('正确渲染组件', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.props().label).toBe('测试多选框')
    expect(wrapper.props().options.length).toBe(3)
    expect(wrapper.props().dir).toBe('row')
    expect(wrapper.props().useColor).toBe(false)
  })

  it('可以正确绑定v-model值', async () => {
    await wrapper.setProps({ value: ['1', '2'] })
    expect(wrapper.vm.currentValue).toEqual(['1', '2'])
  })

  it('在禁用状态下正确显示', async () => {
    await wrapper.setProps({ state: FormState.DISABLED })
    expect(wrapper.vm.isDisabled).toBe(true)
  })

  it('在只读状态下正确显示', async () => {
    await wrapper.setProps({ state: FormState.READONLY })
    expect(wrapper.vm.isReadonly).toBe(true)
  })

  it('可以切换垂直布局', async () => {
    await wrapper.setProps({ dir: 'column' })
    expect(wrapper.props().dir).toBe('column')
  })
})
