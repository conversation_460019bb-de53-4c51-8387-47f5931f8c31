import { mount } from '@vue/test-utils'
import ZDRadio from '@/zd/components/radio/index.vue'
import { FormState } from '@/zd/common/enum'
import { OtherKey } from '@/zd/common/enum'

describe('ZDRadio组件', () => {
  const mockOptions = [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' },
    { label: '其他', value: OtherKey },
  ]

  let wrapper

  beforeEach(() => {
    wrapper = mount(
      {
        template: `<el-form>
      <ZDRadio ref="radio" v-bind="$props" />
      </el-form>`,
        components: {
          ZDRadio,
        },
        props: ZDRadio.props,
      },
      {
        propsData: {
          value: '',
          label: '测试单选框',
          options: mockOptions,
          useColor: false,
          dir: 'row',
          supportInverse: false,
        },
      }
    )
  })

  afterEach(() => {
    wrapper.destroy()
  })

  it('正确渲染组件', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.findAll('.el-radio__label').length).toBe(3)
  })

  it('可以正确修改dir', async () => {
    expect(wrapper.find('.flex.flex-col').exists()).toBe(false)
    await wrapper.setProps({ dir: 'column' })
    expect(wrapper.find('.flex.flex-col').exists()).toBe(true)
  })

  it('在禁用状态下正确显示', async () => {
    expect(wrapper.find('.el-radio__input.is-disabled').exists()).toBe(false)
    await wrapper.setProps({ state: FormState.DISABLED })
    expect(wrapper.find('.el-radio__input.is-disabled').exists()).toBe(true)
  })

  it('在只读状态下正确显示', async () => {
    await wrapper.setProps({ state: FormState.READONLY, value: '2' })
    expect(wrapper.findAll('.el-radio__label').length).toBe(0)
  })

  it('支持反选功能', async () => {
    await wrapper.setProps({ supportInverse: true, value: '1' })

    // 模拟点击事件
    const event = { pointerId: 1 }
    const option = mockOptions[0]

    wrapper.find({ ref: 'radio' }).vm.handleChange(event, option)
    expect(wrapper.find({ ref: 'radio' }).vm.currentValue).toBe('')

    wrapper.find({ ref: 'radio' }).vm.handleChange(event, option)
    expect(wrapper.find({ ref: 'radio' }).vm.currentValue).toBe('1')
  })

  it('不使用反选功能时直接选中', async () => {
    await wrapper.setProps({ supportInverse: false, value: '' })

    // 模拟点击事件
    const event = { pointerId: 1 }
    const option = mockOptions[0]

    wrapper.find({ ref: 'radio' }).vm.handleChange(event, option)
    expect(wrapper.find({ ref: 'radio' }).vm.currentValue).toBe('1')
  })

  it('忽略无效的点击事件', async () => {
    await wrapper.setProps({ value: '1' })

    // 模拟无效点击事件
    const event = { pointerId: 0 }
    const option = mockOptions[1]

    wrapper.find({ ref: 'radio' }).vm.handleChange(event, option)
    expect(wrapper.find({ ref: 'radio' }).vm.currentValue).toBe('1') // 值不应该改变
  })

  it('使用颜色标签时正确显示', async () => {
    await wrapper.setProps({ useColor: true })
    expect(wrapper.findAll('.zd-tag').length).toBe(3)
  })
  it('使用颜色标签和只读时正确显示', async () => {
    await wrapper.setProps({
      useColor: true,
      state: FormState.READONLY,
      value: '1',
    })
    expect(wrapper.findAll('.zd-tag').length).toBe(1)
    expect(wrapper.find('.zd-tag').text()).toBe('选项1')
  })

  it('选中其他出现输入框', async () => {
    expect(wrapper.find('.xn-input').exists()).toBe(false)
    await wrapper.setProps({
      value: OtherKey,
    })
    expect(wrapper.find('.xn-input').exists()).toBe(true)
  })

  it('其他输入框的值', async () => {
    await wrapper.setProps({
      value: OtherKey,
    })
    await wrapper.find('input[type="text"]').setValue('其他')
    expect(wrapper.find({ ref: 'radio' }).vm.currentOtherValue).toBe('其他')
  })
})
