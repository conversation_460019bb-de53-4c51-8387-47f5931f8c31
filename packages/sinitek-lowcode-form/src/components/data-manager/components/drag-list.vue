<template>
  <div ref="wrap" class="dm-drag-list overflow-hidden">
    <slot></slot>
    <i class="hidden cursor-grab cursor-grabbing" />
  </div>
</template>

<script>
import {
  autoScrollForElements,
  autoScrollWindowForElements,
} from '@atlaskit/pragmatic-drag-and-drop-auto-scroll/element'
import {
  draggable,
  dropTargetForElements,
  monitorForElements,
} from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import {
  attachClosestEdge,
  extractClosestEdge,
} from '@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge'
import { disableNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/disable-native-drag-preview'

const duration = 150
// 设置所有元素的 Y 轴偏移动画
const setTranslateY = (elements, targetIdx, offsetY, animate = true) => {
  for (let idx = 0; idx < elements.length; idx++) {
    const node = elements[idx]
    if (!node.style) return
    if (idx > targetIdx) {
      node.animate(
        {
          transform: `translate(0,${offsetY}px)`,
        },
        {
          duration: animate ? duration : 0,
          fill: 'forwards',
          easing: 'cubic-bezier(0.2,0,0,1)',
        }
      )
    } else {
      node.animate(
        {
          transform: `translate(0,0px)`,
        },
        { duration: animate ? duration : 0, fill: 'forwards' }
      )
    }
  }
}

function getElementOuterHeightWithMargin(element) {
  const heightWithPaddingAndBorder = element.offsetHeight // 包括内容、padding、border

  const computedStyle = window.getComputedStyle(element)
  const marginTop = parseFloat(computedStyle.marginTop)
  const marginBottom = parseFloat(computedStyle.marginBottom)

  // 计算总高度（内容 + padding + border + margin）
  return heightWithPaddingAndBorder + marginTop + marginBottom
}

export default {
  name: 'DragList',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    // 拖拽元素
    dragHandle: null,
    group: [Object, String],
    // 滚动容器，默认是父元素
    scrollEl: {
      type: [String, Object],
      default: null,
    },
    // 预览节点，默认是 cloneNode
    previewEl: {
      type: [String, Object],
      default: null,
    },
    // 是否可以拖动
    canDrag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      list: this.value, // 当前渲染的列表
      clean: [], // 拖拽事件清理函数集合
    }
  },
  watch: {
    // 监听 value 变化，自动初始化拖拽
    value: {
      handler(val) {
        this.list = val
        this.$nextTick(() => {
          this.init()
        })
      },
    },
  },
  mounted() {
    // 自动滚动支持
    let el = this.$el.parentElement
    if (typeof this.scrollEl === 'string') {
      el = this.$el.querySelector(this.scrollEl)
    } else if (this.scrollEl instanceof HTMLElement) {
      el = this.scrollEl
    }
    if (!el) {
      return
    }
    this.autoScroll = combine(
      autoScrollForElements({
        element: el,
      }),
      autoScrollWindowForElements()
    )
    // 进入视口时自动初始化拖拽
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          this.$nextTick(() => {
            this.init()
          })
        } else {
          this.init()
        }
      })
    })
    this.observer.observe(this.$el)
  },
  beforeDestroy() {
    this.autoScroll?.destroy?.()
    this?.destroy()
    this.observer?.disconnect?.()
  },
  methods: {
    // 清理所有拖拽事件
    destroy() {
      this.clean.forEach((fn) => fn())
      this.clean = []
    },
    // 初始化拖拽逻辑
    init() {
      this.destroy()
      if (!this.canDrag) {
        return
      }
      this.$nextTick(() => {
        if (this.$slots?.default?.length) {
          this.$slots.default.forEach((vnode, idx) => {
            let lastEnterIdx = -1 // 上一次进入的索引
            let lastEnterEdge = null // 上一次进入的边缘
            // 拖拽数据
            const dragMeta = {
              group: this.group,
              index: idx,
              // 这里的高要加上边框和外边距
              height: getElementOuterHeightWithMargin(vnode.elm),
            }
            let customPreview = null // 拖拽预览节点
            let placeholder = null // 占位节点
            let offsetX = 0
            let offsetY = 0
            const element = vnode.elm
            // 拖拽配置项
            const dragOptions = {
              element,
              getInitialData: () => dragMeta,
              onDragStart: (args) => {
                element.classList.add('cursor-grab')
                // 创建拖动效果组件
                customPreview =
                  typeof this.previewEl === 'string'
                    ? element.querySelector(this.previewEl)
                    : this.previewEl
                if (!customPreview) {
                  customPreview = element
                }
                let pre = customPreview
                customPreview = customPreview.cloneNode(true)
                customPreview.style.position = `fixed`
                customPreview.style.zIndex = `999999`
                // 计算鼠标相对元素的偏移，保证拖拽体验
                const rect = element.getBoundingClientRect()
                offsetX = args.location.initial.input.clientX - rect.left
                offsetY = args.location.initial.input.clientY - rect.top
                customPreview.style.transform = `translate(${rect.left}px, ${rect.top}px)`
                customPreview.style.width = `${pre.offsetWidth}px`
                customPreview.style.height = `${pre.offsetHeight}px`
                customPreview.style.left = `0px`
                customPreview.style.top = `0px`
                customPreview.style.backgroundColor = `#fff`
                customPreview.style.pointerEvents = `none`
                placeholder = document.createElement('div')
                placeholder.style.width = `${element.offsetWidth}px`
                placeholder.style.height = `${dragMeta.height}px`
                placeholder.classList.add('dm-drag-placeholder')
                this.$el.appendChild(placeholder)
                this.$el.parentElement.appendChild(customPreview)
                setTranslateY(this.$el.children, idx, dragMeta.height, false)
                element.classList.add('hidden')
                pre = null
              },
              onGenerateDragPreview({ nativeSetDragImage }) {
                disableNativeDragPreview({
                  nativeSetDragImage,
                })
              },
              onDrop: () => {
                element.classList.remove('hidden')
                setTranslateY(this.$el.children, -1, 0, false)
                if (customPreview) {
                  customPreview.remove()
                  customPreview = null
                }
                if (placeholder) {
                  placeholder.remove()
                  placeholder = null
                }
                element.classList.remove('cursor-grab')
              },
            }
            // 支持自定义拖拽手柄
            if (this.dragHandle) {
              dragOptions.dragHandle =
                typeof this.dragHandle === 'string'
                  ? element.querySelector(this.dragHandle)
                  : this.dragHandle
            }
            // 组合拖拽、监控、放置事件
            this.clean.push(
              combine(
                draggable(dragOptions),
                monitorForElements({
                  onDrag: ({ location }) => {
                    if (customPreview) {
                      customPreview.style.transform = `translate(${
                        location.current.input.clientX - offsetX
                      }px, ${location.current.input.clientY - offsetY}px)`
                    }
                  },
                }),
                dropTargetForElements({
                  element,
                  getIsSticky: () => true,
                  canDrop: ({ source }) => {
                    if (this.group) {
                      if (typeof this.group === 'string') {
                        return this.group === source.data.group
                      }
                      return this.group.name === source.data.group.name
                    }
                    return true
                  },
                  onDragStart: (opts) => {
                    lastEnterIdx = -1
                    this.itemHeight = opts.source.element.offsetHeight
                  },
                  getData: ({ input, element }) => {
                    return attachClosestEdge(dragMeta, {
                      element,
                      input,
                      allowedEdges: ['top', 'bottom'],
                    })
                  },
                  onDrag: ({ self, source }) => {
                    const edge = extractClosestEdge(self.data)
                    if (
                      lastEnterEdge === edge &&
                      lastEnterIdx === self.data.index
                    ) {
                      return
                    }
                    lastEnterEdge = edge
                    lastEnterIdx = self.data.index
                    const toIdx =
                      edge === 'top' ? self.data.index - 1 : self.data.index

                    setTranslateY(this.$el.children, toIdx, source.data.height)
                  },
                  onDrop: ({ source, self }) => {
                    const edge = extractClosestEdge(self.data)
                    const fromIdx = source.data.index
                    const toIdx =
                      edge === 'top' ? self.data.index - 1 : self.data.index
                    if (fromIdx === toIdx) {
                      return
                    }
                    // 根据 from 和 to 位置修改 list，删除 from 位置元素插入到 to 位置
                    const newList = [...this.list]
                    const [movedItem] = newList.splice(fromIdx, 1)
                    newList.splice(
                      toIdx < fromIdx ? toIdx + 1 : toIdx,
                      0,
                      movedItem
                    )
                    this.$emit('input', newList)
                  },
                })
              )
            )
          })
        }
      })
    },
  },
}
</script>
