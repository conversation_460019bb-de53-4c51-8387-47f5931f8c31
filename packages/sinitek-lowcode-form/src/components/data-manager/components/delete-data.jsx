import mitter from '@/utils/mitter'
import { formDataService } from '@/service'
import { MessageBox, Message } from 'element-ui'
export const deleteConfirm = function (h, id, formcode) {
  return new Promise((resolve, reject) => {
    MessageBox({
      title: '您确定要删除所选数据吗？',
      message: h(
        'p',
        {
          class: 'slc-text-red-500',
        },
        '数据删除后无法恢复，引用该数据的功能（如关联表单等）将均会受到影响，请谨慎删除！'
      ),
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '执行中...'
          try {
            let promise = Array.isArray(id)
              ? formDataService.deleteFormDataBatch({ idList: id, formcode })
              : formDataService.deleteFormDataSingle({ id, formcode })
            await promise
            Message.success('删除成功')
            mitter.emit('delete-form-data')
            resolve(true)
          } catch (error) {
            Message.error('删除失败')
          } finally {
            instance.confirmButtonLoading = false
            done()
            resolve(false)
          }
        } else {
          done()
          resolve(false)
        }
      },
    }).catch(() => {
      // 防止报错
    })
  })
}
