import GoDetail from './go-detail.vue'
import ZDTag from '../../../zd/common/tag'
import { Components } from '@/zd/common/enum'
export default {
  name: 'DMCell',
  components: {
    GoDetail,
  },
  props: {
    // 当前列的配置信息
    item: {
      type: Object,
      required: true,
    },
    // 当前行的数据对象
    row: {
      type: Object,
      required: true,
    },
  },
  render() {
    // 判断是否为子表单类型，渲染"查看详情"按钮
    if (this.item.componentName === Components.ZDChildForm) {
      return <GoDetail text="查看详情" row={this.row} />
    } else if (this.item.componentName === Components.ZDDateRange) {
      return <span>{this.row[this.item.ref]?.join('~') ?? '--'}</span>
    } else if (this.item.componentName === Components.ZDEmployee) {
      // 组织结构组件
      const val = this.row[this.item.ref]
        ?.split(',')
        .map((e) => e.split(':')[1])
      if (val) {
        return <span>{val.join(',')}</span>
      }
      return <span>--</span>
    } else if (
      // 判断是否为单选、多选、复选等类型
      [
        Components.ZDRadio,
        Components.ZDSelectSingle,
        Components.ZDSelectMultiple,
        Components.ZDCheckbox,
      ].includes(this.item.componentName)
    ) {
      // 获取当前单元格的值
      const value = this.row[this.item.ref] || ''
      // 统一转为数组，过滤掉 null
      const arr = (Array.isArray(value) ? value : value ? [value] : []).filter(
        (e) => e != null
      )
      // 如果没有值，显示占位符
      if (arr.length === 0) {
        return <span>--</span>
      }
      return (
        <div>
          {/* 遍历所有选项，分别渲染 */}
          {arr.map((e, i) => {
            const spl = e.split(':') // 拆分 value:label 结构
            if (this.item.props.useColor) {
              // 如果启用颜色，查找对应颜色并用标签组件展示
              const _find = this.item.props.options.find(
                (e) => e.value === spl[0]
              )
              return (
                <ZDTag
                  item={{
                    value: spl[0],
                    label: spl[1],
                    color: _find.color,
                  }}
                />
              )
            } else {
              // 否则直接用文本显示，label优先，value兜底
              return (
                <span>
                  {i ? ',' : ''}
                  {spl[1] || spl[0] || '--'}
                </span>
              )
            }
          })}
        </div>
      )
    }
    // 其他类型直接显示内容，数组用逗号拼接
    const isArr = Array.isArray(this.row[this.item.ref])
    return (
      <span>
        {isArr
          ? this.row[this.item.ref].join(',')
          : this.row[this.item.ref] || '--'}
      </span>
    )
  },
}
