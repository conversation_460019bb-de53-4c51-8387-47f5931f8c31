<template>
  <el-dropdown class="ml-3" @command="handleCommand">
    <span class="h-7 px-2 icon-hover"> ...更多 </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="item in items"
        :key="item.label"
        :disabled="item.disabled"
        :command="item.command"
      >
        {{ item.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { deleteConfirm } from '../delete-data'
export default {
  name: 'ZDDataManagerToolbarMore',
  inject: ['DM'],
  data() {
    return {}
  },
  computed: {
    items() {
      return [
        {
          label: `删除选中(共${this.DM.selectedRows.length}条)`,
          disabled: this.DM.selectedRows.length === 0,
          command: 'deleteSelected',
        },
      ]
    },
  },
  methods: {
    handleCommand(command) {
      if (command === 'deleteSelected') {
        deleteConfirm(
          this.$createElement,
          this.DM.selectedRows.map((item) => item.id),
          this.$route.params.formcode
        ).then(() => {
          this.DM.getTableData()
        })
      }
    },
  },
}
</script>
