<template>
  <aside>
    <el-input
      v-model="filterName"
      clearable
      size="mini"
      placeholder="请输入筛选条件"
      class="mb-2"
    >
      <i slot="suffix" class="el-input__icon el-icon-search" />
    </el-input>
    <ElScrollbar tag="ul" wrap-class="w-full max-h-50">
      <!-- 可以筛选字段的列表 -->
      <li
        v-for="(item, index) in compItems"
        :key="item.id"
        class="slc-filter-item flex rounded px-2 py-1 important:justify-start"
        :class="[
          isSelected(item) ? 'text-gray-400 cursor-not-allowed' : 'icon-hover',
        ]"
        @click="onSelect(item, index)"
      >
        <span>{{ item.label }}</span>
      </li>
    </ElScrollbar>
  </aside>
</template>

<script>
import ElScrollbar from 'element-ui/lib/scrollbar'
export default {
  name: 'DMSelectList',
  components: {
    ElScrollbar,
  },
  props: {
    items: Array,
    placeholder: String,
    value: Array,
  },
  data() {
    return {
      currentValue: this.value,
      filterName: '',
    }
  },
  computed: {
    compItems() {
      return this.items.filter((item) => item.label.includes(this.filterName))
    },
  },
  watch: {
    value(v) {
      this.currentValue = v
    },
  },
  methods: {
    isSelected(item) {
      return this.value.find((i) => i.value === item.value)
    },
    onSelect(item, index) {
      if (this.isSelected(item)) {
        return
      }
      this.currentValue.push(item)
      this.$emit('input', this.currentValue)
    },
  },
}
</script>
