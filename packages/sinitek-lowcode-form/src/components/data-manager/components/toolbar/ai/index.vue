<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="ai-assistant flex-shrink-0">
    <!-- 浮动按钮 -->
    <div class="ai-float-button mr-2 flex-shrink-0" @click="toggleChat">
      AI分析
    </div>

    <!-- 聊天界面 -->
    <div
      v-if="showChat"
      class="ai-chat-container"
      :class="{
        'ai-chat-dragging': isDragging,
        'ai-chat-expanded': isExpanded,
      }"
      :style="chatContainerStyle"
    >
      <!-- 聊天头部 -->
      <div class="ai-chat-header" @mousedown="startDrag">
        <div class="ai-chat-title">
          <AIIcon class="ai-header-icon" />
          <span>{{ name }}</span>
        </div>
        <div class="ai-chat-actions">
          <div
            class="ai-chat-expand"
            :title="isExpanded ? '恢复宽度' : '扩展宽度'"
            @click="toggleExpanded"
          >
            <i
              :class="
                isExpanded
                  ? 'i-mingcute:contract-left-line'
                  : 'i-mingcute:expand-width-line'
              "
            ></i>
          </div>
          <div
            class="ai-chat-debug"
            title="调试模式"
            @click="debugSimulateMessage"
          >
            <i class="el-icon-setting"></i>
          </div>
          <div
            class="ai-chat-reset"
            title="发起新对话"
            @click="resetConversation"
          >
            <i class="el-icon-plus"></i>
          </div>
          <div class="ai-chat-close" title="关闭" @click="toggleChat">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div ref="chatContent" class="ai-chat-content">
        <div class="ai-chat-messages">
          <!-- 欢迎消息 -->
          <div class="ai-message ai-message-bot">
            <div class="ai-message-content">
              <div class="ai-message-text user-select-text ai-example-text">
                <div class="ai-welcome-text">
                  Hi，我可以帮你分析数据并生成图表，你只需要提供分析需求，比如你可以这么对我说：
                </div>
                <div class="ai-welcome-examples">
                  <div
                    class="ai-example-link"
                    @click="fillExample('帮我分析一下用户数据的分布情况')"
                  >
                    "帮我分析一下用户数据的分布情况"
                  </div>
                  <div
                    class="ai-example-link"
                    @click="fillExample('生成数据流程图')"
                  >
                    "生成数据流程图"
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 动态消息列表 -->
          <div
            v-for="message in messages"
            :key="message.id"
            class="ai-message"
            :class="[
              message.type === 'user' ? 'ai-message-user' : 'ai-message-bot',
              { 'ai-message-completion': message.isCompletion },
            ]"
          >
            <div v-if="message.type === 'bot'" class="ai-message-avatar">
              {{ name }}
            </div>
            <div class="ai-message-content">
              <div
                class="ai-message-text"
                :class="{
                  'user-select-text': true,
                  'ai-completion-text': message.isCompletion,
                }"
              >
                <!-- 如果是HTML内容，使用v-html渲染 -->
                <div v-if="message.isHtml" class="ai-html-content-wrapper">
                  <div class="ai-html-content" v-html="message.content"></div>
                </div>
                <!-- 否则使用普通文本 -->
                <template v-else>{{ message.content }}</template>
              </div>
              <div class="ai-message-actions">
                <div v-if="message.timestamp" class="ai-message-time">
                  {{ formatTime(message.timestamp) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 等待状态显示 -->
          <div v-if="sending" class="ai-message ai-message-bot">
            <div class="ai-message-avatar">
              {{ name }}
              <!-- <div class="ai-loading-spinner"></div> -->
            </div>
            <div class="ai-message-content">
              <div class="ai-message-text ai-waiting-message">
                AI正在思考中...
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天输入区域 -->
      <div class="ai-chat-input">
        <div class="ai-input-container">
          <div class="ai-input-wrapper">
            <!-- 图片上传区域 -->
            <div class="ai-upload-section">
              <div class="ai-upload-header">
                <span class="ai-upload-label">
                  上传图片（可选）
                  <span
                    v-if="selectedImages.length > 0"
                    class="ai-upload-count"
                  >
                    已选择 {{ selectedImages.length }} 个
                  </span>
                </span>
                <div class="ai-upload-actions">
                  <button
                    v-if="selectedImages.length > 0"
                    class="ai-upload-clear"
                    title="清空已选择的图片"
                    @click="clearSelectedImages"
                  >
                    <svg-icon icon-class="remove" />
                    清空
                  </button>
                  <button
                    v-if="!showImages"
                    class="ai-upload-toggle"
                    @click="toggleImages"
                  >
                    <svg-icon icon-class="plus" />
                    添加图片
                  </button>
                  <button v-else class="ai-upload-toggle" @click="toggleImages">
                    <i class="el-icon-document-copy"></i>
                    收起
                  </button>
                </div>
              </div>

              <div v-if="showImages" class="ai-upload-content">
                <div class="ai-image-upload-container">
                  <!-- 图片上传按钮 -->
                  <div
                    v-if="selectedImages.length < 6"
                    class="ai-image-upload-button"
                    @click="triggerImageUpload"
                  >
                    <svg-icon icon-class="plus" />
                    <span>选择图片</span>
                    <div class="ai-image-upload-hint">
                      最多6张，支持jpg/png/gif
                    </div>
                  </div>

                  <!-- 隐藏的文件输入框 -->
                  <input
                    ref="imageInput"
                    type="file"
                    accept="image/*"
                    multiple
                    style="display: none"
                    @change="handleImageSelect"
                  />

                  <!-- 已选择的图片预览 -->
                  <div
                    v-for="(image, index) in selectedImages"
                    :key="index"
                    class="ai-image-preview"
                  >
                    <img :src="image.preview" :alt="image.name" />
                    <div class="ai-image-overlay">
                      <div class="ai-image-name">{{ image.name }}</div>
                      <button
                        class="ai-image-remove"
                        title="删除图片"
                        @click="removeImage(index)"
                      >
                        <svg-icon icon-class="remove" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 多行文本输入框 -->
            <div class="ai-textarea-wrapper">
              <textarea
                ref="textareaInput"
                v-model="inputMessage"
                class="ai-textarea"
                placeholder="请输入你的需求，按Ctrl+Enter发送"
                rows="10"
                :maxlength="1000"
                @keydown="handleKeydown"
                @focus="handleInputFocus"
              ></textarea>

              <div
                class="ai-input-counter"
                :class="{
                  warning: inputMessage.length >= 800,
                  danger: inputMessage.length >= 950,
                }"
              >
                {{ inputMessage.length }}/1000
              </div>

              <button
                class="ai-send-button"
                :disabled="!inputMessage.trim() || sending"
                @click="sendMessage"
              >
                <svg-icon v-if="!sending" icon-class="send" />
                <i v-else class="ai-loading el-icon-loader"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mermaid图表浮层 - 左右分屏编辑预览模式 -->
    <div
      v-if="showMermaidModal"
      class="mermaid-overlay"
      @click="closeMermaidModal"
    >
      <div class="mermaid-editor-popup" @click.stop>
        <!-- 头部工具栏 -->
        <div class="mermaid-editor-header">
          <div class="mermaid-editor-title">
            <i class="i-mingcute:flow-chart-line"></i>
            <span>Mermaid 图表编辑器</span>
          </div>
          <div class="mermaid-editor-actions">
            <button
              class="mermaid-action-btn"
              title="重置代码"
              @click="resetMermaidCode"
            >
              <i class="i-mingcute:refresh-1-line"></i>
            </button>
            <button
              class="mermaid-action-btn"
              title="复制代码"
              @click="copyMermaidCode"
            >
              <i class="i-mingcute:copy-line"></i>
            </button>
            <button
              class="mermaid-action-btn"
              title="下载SVG"
              @click="downloadMermaidSvg"
            >
              <i class="i-mingcute:download-line"></i>
            </button>
            <button class="mermaid-close-btn" @click="closeMermaidModal">
              <i class="i-mingcute:close-line"></i>
            </button>
          </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="mermaid-editor-content">
          <!-- 左侧编辑区域 -->
          <div class="mermaid-editor-left">
            <div class="mermaid-editor-panel-header">
              <span>编辑区域</span>
              <div class="mermaid-editor-panel-actions">
                <button
                  class="mermaid-format-btn"
                  title="格式化代码"
                  @click="formatMermaidCode"
                >
                  <i class="i-mingcute:code-line"></i>
                  格式化
                </button>
              </div>
            </div>
            <div class="mermaid-code-editor">
              <textarea
                ref="mermaidCodeEditor"
                v-model="editableMermaidCode"
                class="mermaid-textarea"
                placeholder="请输入 Mermaid 代码..."
                spellcheck="false"
                @input="onMermaidCodeChange"
                @keydown.tab.prevent="insertTab"
              ></textarea>
              <!-- 代码编辑器状态栏 -->
              <div class="mermaid-editor-statusbar">
                <span class="mermaid-char-count"
                  >{{ editableMermaidCode.length }} 字符</span
                >
                <span class="mermaid-line-count">{{ getLineCount() }} 行</span>
                <span
                  class="mermaid-syntax-status"
                  :class="{ error: mermaidSyntaxError }"
                >
                  {{ mermaidSyntaxError ? '语法错误' : '语法正确' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 分割线 -->
          <div class="mermaid-editor-divider"></div>

          <!-- 右侧预览区域 -->
          <div class="mermaid-editor-right">
            <div class="mermaid-editor-panel-header">
              <span>预览区域</span>
              <div class="mermaid-editor-panel-actions">
                <button
                  class="mermaid-refresh-btn"
                  title="刷新预览"
                  @click="refreshMermaidPreview"
                >
                  <i class="i-mingcute:refresh-line"></i>
                  刷新
                </button>
                <button
                  class="mermaid-fullscreen-btn"
                  title="全屏预览"
                  @click="toggleFullscreenPreview"
                >
                  <i class="i-mingcute:fullscreen-line"></i>
                </button>
              </div>
            </div>
            <div class="mermaid-preview-container">
              <!-- 加载状态 -->
              <div v-if="mermaidRendering" class="mermaid-loading">
                <i class="mermaid-loading-icon i-mingcute:loading-line"></i>
                <span>渲染中...</span>
              </div>
              <!-- 错误状态 -->
              <div v-else-if="mermaidSyntaxError" class="mermaid-error">
                <i class="i-mingcute:alert-triangle-line"></i>
                <div class="mermaid-error-message">
                  <h4>语法错误</h4>
                  <p>{{ mermaidSyntaxError }}</p>
                </div>
              </div>
              <!-- 预览区域 -->
              <div
                v-else
                :id="'mermaid-preview-' + mermaidPreviewId"
                ref="mermaidPreview"
                class="mermaid-preview"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { log } from 'sinitek-lowcode-shared'
import { http } from '@/utils/http'
export default {
  name: 'AIAssistant',
  inject: {
    $doc: {
      default: () => ({}),
    },
    fetcher: {
      default: () => ({}),
    },
    message: {
      default: () => ({}),
    },
    // 上面的都可以删掉了  没有了
    DM: {
      default: () => ({}),
    },
  }, // 注入文档对象、fetcher和消息对象
  data() {
    return {
      name: 'AI分析',
      showChat: false,
      inputMessage: '',
      sending: false,
      messages: [], // 聊天消息
      messageIdCounter: 1,
      sessionId: null, // AI会话ID
      // 拖拽相关
      isDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      chatPosition: {
        x: 0,
        y: 0,
      },

      // 图片上传相关
      showImages: false, // 是否显示图片上传区域
      selectedImages: [], // 选中的图片文件，格式为 [{file: File, name: string, preview: string}]
      // 扩展宽度相关
      isExpanded: false, // 是否扩展宽度模式
      showMermaidModal: false, // 是否显示mermaid弹窗
      mermaidModalContent: '', // mermaid弹窗内容
      // Mermaid编辑器相关
      editableMermaidCode: '', // 可编辑的mermaid代码
      originalMermaidCode: '', // 原始mermaid代码
      mermaidRendering: false, // 是否正在渲染
      mermaidSyntaxError: '', // 语法错误信息
      mermaidPreviewId: 0, // 预览区域ID计数器
      mermaidRenderTimeout: null, // 渲染延时器
    }
  },
  computed: {
    chatContainerStyle() {
      return {
        transform: `translate(${this.chatPosition.x}px, ${this.chatPosition.y}px)`,
      }
    },
  },
  mounted() {
    // 设置初始位置为右下角
    this.setInitialPosition()
  },
  beforeDestroy() {
    // 清理拖拽事件监听器
    document.removeEventListener('mousemove', this.onDrag)
    document.removeEventListener('mouseup', this.stopDrag)
  },
  methods: {
    // 处理键盘事件
    handleKeydown(event) {
      // Ctrl+Enter 或 Shift+Enter 发送消息
      if (event.key === 'Enter' && (event.ctrlKey || event.shiftKey)) {
        event.preventDefault()
        this.sendMessage()
      }
      // 普通 Enter 键允许换行（不阻止默认行为）
    },

    // 处理输入框聚焦事件
    handleInputFocus() {
      // 聚焦输入框时收起图片选择区域
      this.showImages = false
    },

    // 切换图片上传区域显示/隐藏
    toggleImages() {
      this.showImages = !this.showImages
    },

    // 清空已选择的图片
    clearSelectedImages() {
      // 释放预览URL内存
      this.selectedImages.forEach((image) => {
        if (image.preview) {
          URL.revokeObjectURL(image.preview)
        }
      })
      this.selectedImages = []
    },

    // 触发图片选择
    triggerImageUpload() {
      if (this.$refs.imageInput) {
        this.$refs.imageInput.click()
      }
    },

    // 处理图片选择
    handleImageSelect(event) {
      const files = Array.from(event.target.files)
      if (!files.length) return

      // 检查总数量限制
      const remainingSlots = 6 - this.selectedImages.length
      if (remainingSlots <= 0) {
        this.message?.warning('最多只能选择6张图片')
        return
      }

      // 取前面可用的文件
      const filesToProcess = files.slice(0, remainingSlots)
      const validImages = []

      filesToProcess.forEach((file) => {
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
          this.message?.warning(`文件 ${file.name} 不是有效的图片格式`)
          return
        }

        // 验证文件大小（限制为10MB）
        if (file.size > 10 * 1024 * 1024) {
          this.message?.warning(`图片 ${file.name} 大小超过10MB限制`)
          return
        }

        // 创建预览URL
        const preview = URL.createObjectURL(file)
        validImages.push({
          file,
          name: file.name,
          preview,
        })
      })

      // 添加到已选择的图片列表
      this.selectedImages.push(...validImages)

      // 清空input值，允许重复选择同一文件
      event.target.value = ''

      if (validImages.length > 0) {
        this.message?.success(`成功添加 ${validImages.length} 张图片`)
      }
    },

    // 删除指定图片
    removeImage(index) {
      const image = this.selectedImages[index]
      if (image && image.preview) {
        // 释放预览URL内存
        URL.revokeObjectURL(image.preview)
      }
      this.selectedImages.splice(index, 1)
    },

    // 设置初始位置
    setInitialPosition() {
      // 设置为右下角位置，距离右边和上边各20px
      const containerWidth = this.isExpanded ? 760 : 380 // 根据扩展状态调整宽度
      this.chatPosition.x = window.innerWidth - containerWidth - 20
      this.chatPosition.y = 20
    },

    // 切换扩展宽度
    toggleExpanded() {
      this.isExpanded = !this.isExpanded
      // 重新计算位置，确保扩展后不会超出屏幕
      this.adjustPositionAfterExpand()
      // 重新调整所有mermaid图表的尺寸
      this.$nextTick(() => {
        this.adjustAllMermaidSizes()
      })
    },

    // 扩展后调整位置
    adjustPositionAfterExpand() {
      const containerWidth = this.isExpanded ? 760 : 380
      const minVisibleWidth = 100

      // 确保扩展后不会超出屏幕右边界
      const maxX = window.innerWidth - minVisibleWidth
      if (this.chatPosition.x + containerWidth > window.innerWidth) {
        this.chatPosition.x = Math.max(
          maxX - containerWidth,
          -containerWidth + minVisibleWidth
        )
      }
    },

    toggleChat() {
      this.showChat = !this.showChat
      if (this.showChat) {
        this.$nextTick(() => {
          this.scrollToBottom()
          // 聚焦到文本输入框
          const textareaInput = this.$refs.textareaInput
          if (textareaInput) {
            textareaInput.focus()
          }
        })
      } else {
        // 关闭聊天时重置会话
        this.resetSession()
      }
    },

    // 重置会话
    resetSession() {
      this.sessionId = null
      this.sending = false
    },

    // 重置对话
    resetConversation() {
      // 如果AI正在处理，不允许重置对话
      if (this.sending) {
        this.message?.warning('AI正在处理中，请等待完成后再重置对话')
        return
      }

      // 清空聊天记录和会话ID
      this.messages = []
      this.sessionId = null

      // 重置会话状态
      this.sending = false

      // 清空输入框和选中的图片
      this.inputMessage = ''
      this.clearSelectedImages()
      this.showImages = false

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.sending) return

      // 构建用户消息显示内容
      let displayContent = this.inputMessage.trim()
      if (this.selectedImages.length > 0) {
        const imageNames = this.selectedImages.map((image) => image.name)
        displayContent += `\n\n🖼️ 上传的图片: ${imageNames.join(', ')}`
      }

      const userMessage = {
        id: this.messageIdCounter++,
        type: 'user',
        content: displayContent,
        timestamp: new Date(),
      }

      // 添加到消息数组
      this.messages.push(userMessage)
      const userInput = this.inputMessage.trim()

      // 清空输入框和选中的图片
      this.inputMessage = ''
      this.clearSelectedImages() // 使用方法清空图片，释放内存
      this.sending = true

      // 立即滚动到底部显示等待状态
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      try {
        // AI 分析 - 调用图表生成API
        await this.generateDiagramResponse(userInput)
      } catch (error) {
        console.error('发送消息失败:', error)
        this.addBotMessage('抱歉，服务暂时不可用，请稍后再试。')
      } finally {
        this.sending = false
      }
    },

    // 调用AI生成图表API
    async callAIGenerateDiagramAPI(userInput) {
      try {
        // 准备请求参数
        const formData = new FormData()
        formData.append('prompt', userInput)
        formData.append('formCode', this.DM.formcode) // 获取当前表单的formCode

        // 如果有会话ID，添加到请求中
        if (this.sessionId) {
          formData.append('sessionId', this.sessionId)
        }

        // 发送请求
        const result = await http.post(
          '/zhida/frontend/api/nocode/llm/agent-generate-diagram',
          formData
        )

        // 保存sessionId（如果返回了）
        if (result.data && result.data.sessionId) {
          this.sessionId = result.data.sessionId
        }

        return result
      } catch (error) {
        console.error('调用AI生成图表API失败:', error)
        throw error
      }
    },

    // 解析并渲染mermaid图表
    parseMermaidContent(text) {
      // 匹配mermaid代码块
      const mermaidRegex = /```mermaid\n([\s\S]*?)\n```/g
      let match
      let processedText = text

      while ((match = mermaidRegex.exec(text)) !== null) {
        const mermaidCode = match[1].trim()

        // 基本验证 - 检查是否为空
        if (!mermaidCode || mermaidCode.length < 3) {
          const errorDiv = `<div class="mermaid-container">
            <div style="color: #ef4444; padding: 15px; border: 1px solid #fecaca; border-radius: 8px; background: #fef2f2;">
              ⚠️ 图表内容为空
            </div>
          </div>`
          processedText = processedText.replace(match[0], errorDiv)
          continue
        }

        const mermaidId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        const debugBtnId = `debug-btn-${mermaidId}`

        // 修复：不要对mermaidCode进行HTML转义，直接使用原始代码
        // 添加mermaid图表和调试按钮
        const mermaidDiv = `<div class="mermaid-container">
          <div id="${mermaidId}" class="mermaid" data-mermaid-code="${encodeURIComponent(mermaidCode)}">${mermaidCode}</div>
          <div class="mermaid-toolbar">
            <button 
              id="${debugBtnId}" 
              class="mermaid-debug-btn" 
              data-mermaid-id="${mermaidId}"
              data-mermaid-code="${encodeURIComponent(mermaidCode)}"
              title="在编辑器中调试此图表"
            >
              <i class="i-mingcute:code-line"></i>
              <span>调试编辑</span>
            </button>
          </div>
        </div>`

        processedText = processedText.replace(match[0], mermaidDiv)
      }

      return processedText
    },

    // HTML转义函数
    escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    },

    // 渲染mermaid图表
    async renderMermaidCharts() {
      console.log('=== renderMermaidCharts 开始 ===')

      await this.$nextTick()

      // 如果页面有mermaid图表，需要重新渲染
      const mermaidElements = document.querySelectorAll(
        '.mermaid:not([data-processed])'
      )
      console.log('查找到的未处理mermaid元素:', mermaidElements.length)

      if (mermaidElements.length > 0) {
        try {
          // 确保mermaid库已加载
          await this.ensureMermaidLoaded()
          console.log('mermaid库已准备完成，开始渲染')

          // 渲染图表
          await this.simpleMermaidRender(mermaidElements)
          console.log('图表渲染完成')
        } catch (error) {
          console.error('mermaid渲染过程失败:', error)
          mermaidElements.forEach((element) => {
            element.innerHTML =
              '<div style="color: #ef4444; padding: 10px;">图表渲染失败: ' +
              error.message +
              '</div>'
          })
        }
      } else {
        console.log('没有找到需要渲染的mermaid元素')
      }

      // 添加调试按钮事件监听器
      this.addMermaidDebugListeners()

      console.log('=== renderMermaidCharts 结束 ===')
    },

    // 使用弹窗同样的简单渲染方法
    async simpleMermaidRender(elements) {
      try {
        console.log('=== 使用简单渲染方法 ===')
        console.log('待渲染元素数量:', elements.length)

        // 使用default主题配置，优化图表渲染尺寸
        window.mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          // 设置更大的默认尺寸
          themeConfig: {
            primaryColor: '#ff6b6b',
            primaryTextColor: '#333',
            primaryBorderColor: '#ff6b6b',
            lineColor: '#333',
            secondaryColor: '#fff',
            tertiaryColor: '#fff',
          },
          // 图表配置
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis',
          },
          pie: {
            useMaxWidth: true,
          },
          gantt: {
            useMaxWidth: true,
          },
          journey: {
            useMaxWidth: true,
          },
          gitgraph: {
            useMaxWidth: true,
          },
          // 新增 XY 图表支持
          xyChart: {
            useMaxWidth: true,
            width: 700,
            height: 500,
          },
          // 其他新支持的图表类型
          radar: {
            useMaxWidth: true,
          },
          block: {
            useMaxWidth: true,
          },
          packet: {
            useMaxWidth: true,
          },
          kanban: {
            useMaxWidth: true,
          },
          architecture: {
            useMaxWidth: true,
          },
        })

        // 使用for循环代替forEach以支持异步处理
        for (let index = 0; index < elements.length; index++) {
          const element = elements[index]
          try {
            console.log(`--- 处理第${index + 1}个元素 ---`)

            const originalCode = element.textContent.trim()
            console.log('原始代码:', originalCode)

            // 基本验证
            if (!originalCode || originalCode.length < 3) {
              console.log('代码为空，跳过')
              element.innerHTML =
                '<div style="color: #ef4444; padding: 10px;">图表内容为空</div>'
              continue
            }

            // 清空元素内容，准备重新渲染
            element.innerHTML = ''

            // 标记为已处理
            element.setAttribute('data-processed', 'true')

            // 使用 mermaid.render 方法替代 init 方法
            console.log('调用 mermaid.render...')
            const graphId = `graph-${Date.now()}-${Math.random()
              .toString(36)
              .substr(2, 9)}`

            // 尝试使用新的 render API
            if (typeof window.mermaid.render === 'function') {
              try {
                const { svg } = await window.mermaid.render(
                  graphId,
                  originalCode
                )
                console.log('mermaid.render 成功，SVG长度:', svg.length)
                element.innerHTML = svg
                // 强制调整SVG尺寸以撑满容器
                await this.$nextTick()
                this.adjustMermaidSvgSize(element)
              } catch (renderError) {
                console.log('新API失败，使用旧API...')
                // 回退到旧的回调式API
                window.mermaid.render(graphId, originalCode, (svgCode) => {
                  console.log(
                    'mermaid.render 回调成功，SVG长度:',
                    svgCode.length
                  )
                  element.innerHTML = svgCode
                  // 强制调整SVG尺寸以撑满容器
                  this.$nextTick(() => {
                    this.adjustMermaidSvgSize(element)
                  })
                })
              }
            } else {
              // 如果没有 render 方法，回退到 init
              console.log('使用 mermaid.init 方法...')
              element.textContent = originalCode
              window.mermaid.init(undefined, element)

              console.log('渲染完成，内容长度:', element.innerHTML.length)
              console.log('包含SVG:', element.innerHTML.includes('<svg'))

              // 强制调整SVG尺寸以撑满容器
              await this.$nextTick()
              this.adjustMermaidSvgSize(element)
            }
          } catch (error) {
            console.error('单个图表渲染失败:', error)
            element.innerHTML =
              '<div style="color: #ef4444; padding: 10px;">图表渲染失败: ' +
              error.message +
              '</div>'
          }
        }
        console.log('=== 简单渲染结束 ===')
      } catch (error) {
        console.error('mermaid渲染失败:', error)
        elements.forEach((element) => {
          element.innerHTML =
            '<div style="color: #ef4444; padding: 10px;">渲染引擎错误: ' +
            error.message +
            '</div>'
        })
      }
    },

    // 调整所有Mermaid图表尺寸
    adjustAllMermaidSizes() {
      try {
        const mermaidElements = document.querySelectorAll('.mermaid')
        mermaidElements.forEach((element) => {
          this.adjustMermaidSvgSize(element)
        })
      } catch (error) {
        console.error('调整所有mermaid尺寸失败:', error)
      }
    },

    // 调整Mermaid SVG尺寸以撑满容器
    adjustMermaidSvgSize(element) {
      try {
        const svg = element.querySelector('svg')
        if (!svg) {
          console.log('未找到SVG元素')
          return
        }

        // 获取容器宽度
        const containerWidth =
          element.offsetWidth || element.parentElement.offsetWidth
        console.log('容器宽度:', containerWidth)

        if (containerWidth > 0) {
          // 获取SVG的viewBox或原始尺寸
          let originalViewBox = svg.getAttribute('viewBox')
          let originalWidth, originalHeight

          if (originalViewBox) {
            // 如果有viewBox，从中提取宽高
            const viewBoxValues = originalViewBox.split(/\s+/)
            if (viewBoxValues.length >= 4) {
              originalWidth = parseFloat(viewBoxValues[2])
              originalHeight = parseFloat(viewBoxValues[3])
            }
          }

          // 如果没有从viewBox获取到尺寸，尝试从属性获取
          if (!originalWidth || !originalHeight) {
            originalWidth =
              parseFloat(svg.getAttribute('width')) ||
              svg.getBoundingClientRect().width ||
              300 // 默认宽度
            originalHeight =
              parseFloat(svg.getAttribute('height')) ||
              svg.getBoundingClientRect().height ||
              200 // 默认高度
          }

          console.log('SVG原始尺寸:', {
            originalWidth,
            originalHeight,
            originalViewBox,
          })

          // 设置合理的最小尺寸
          const minHeight = 200
          const minWidth = containerWidth - 40 // 减去padding

          // 计算缩放比例
          const aspectRatio = originalHeight / originalWidth
          let targetWidth = minWidth
          let targetHeight = targetWidth * aspectRatio

          // 确保高度不低于最小值
          if (targetHeight < minHeight) {
            targetHeight = minHeight
          }

          console.log('目标尺寸:', { targetWidth, targetHeight })

          // 重新设置viewBox以确保内容撑满容器
          if (!originalViewBox && originalWidth && originalHeight) {
            svg.setAttribute(
              'viewBox',
              `0 0 ${originalWidth} ${originalHeight}`
            )
          }

          // 强制设置SVG尺寸和样式
          svg.style.width = '100%'
          svg.style.height = `${targetHeight}px`
          svg.style.minHeight = `${minHeight}px`
          svg.style.maxWidth = '100%'
          svg.style.display = 'block'
          svg.style.margin = '0 auto'

          // 移除原来的width和height属性，让CSS控制
          svg.removeAttribute('width')
          svg.removeAttribute('height')

          // 设置preserveAspectRatio让内容撑满
          svg.setAttribute('preserveAspectRatio', 'xMidYMid slice')

          // 如果图表内容过小，尝试放大viewBox
          const svgRect = svg.getBoundingClientRect()
          if (svgRect.width > 0 && svgRect.height > 0) {
            // 获取图表内容的实际大小
            const bbox = svg.getBBox()
            if (bbox.width > 0 && bbox.height > 0) {
              // 添加一些边距
              const padding = Math.min(bbox.width, bbox.height) * 0.1
              const newViewBox = `${bbox.x - padding} ${bbox.y - padding} ${bbox.width + padding * 2} ${bbox.height + padding * 2}`
              svg.setAttribute('viewBox', newViewBox)
              console.log('更新viewBox为:', newViewBox)
            }
          }

          console.log('SVG尺寸调整完成')
        }
      } catch (error) {
        console.error('调整SVG尺寸失败:', error)
      }
    },

    // 移除图表点击事件，只保留调试按钮功能
    // addClickToElement 方法已移除，图表不再支持点击查看

    // 添加Mermaid调试按钮事件监听器
    addMermaidDebugListeners() {
      // 查找所有未添加事件的调试按钮
      const debugButtons = document.querySelectorAll(
        '.mermaid-debug-btn:not([data-listener-added])'
      )

      debugButtons.forEach((button) => {
        const mermaidCode = button.getAttribute('data-mermaid-code')
        const mermaidId = button.getAttribute('data-mermaid-id')

        if (mermaidCode) {
          button.addEventListener('click', (e) => {
            e.stopPropagation() // 防止触发图表的点击事件
            // 修复：使用decodeURIComponent解码
            const decodedCode = decodeURIComponent(mermaidCode)
            this.openMermaidModal(mermaidId, decodedCode)
          })

          // 标记已添加事件监听器
          button.setAttribute('data-listener-added', 'true')
        }
      })
    },

    // 解码HTML转义字符
    unescapeHtml(text) {
      const div = document.createElement('div')
      div.innerHTML = text
      return div.textContent || div.innerText || ''
    },

    // 调整弹窗中的Mermaid图表尺寸（3倍放大）
    adjustModalMermaidSize(element, chatDimensions = null) {
      try {
        const svg = element.querySelector('svg')
        if (!svg) {
          console.log('弹窗中未找到SVG元素')
          return
        }

        // 获取弹窗容器的尺寸
        const modalContainer = document.querySelector(
          '.mermaid-chart-container'
        )
        if (!modalContainer) return

        const containerWidth = modalContainer.offsetWidth - 40 // 减去padding
        const containerHeight = modalContainer.offsetHeight - 40

        console.log('弹窗容器尺寸:', { containerWidth, containerHeight })

        // 获取SVG的viewBox或原始尺寸
        let originalViewBox = svg.getAttribute('viewBox')
        let originalWidth, originalHeight

        if (originalViewBox) {
          const viewBoxValues = originalViewBox.split(/\s+/)
          if (viewBoxValues.length >= 4) {
            originalWidth = parseFloat(viewBoxValues[2])
            originalHeight = parseFloat(viewBoxValues[3])
          }
        }

        if (!originalWidth || !originalHeight) {
          originalWidth = parseFloat(svg.getAttribute('width')) || 300
          originalHeight = parseFloat(svg.getAttribute('height')) || 200
        }

        console.log('弹窗SVG原始尺寸:', { originalWidth, originalHeight })

        // 如果有聊天框尺寸信息，基于其进行3倍放大
        let targetWidth, targetHeight

        if (chatDimensions && chatDimensions.width && chatDimensions.height) {
          // 基于聊天框的实际显示尺寸进行3倍放大
          targetWidth = Math.min(containerWidth, chatDimensions.width * 3)
          targetHeight = Math.min(containerHeight, chatDimensions.height * 3)

          console.log('基于聊天框尺寸放大:', {
            chat: chatDimensions,
            target: { targetWidth, targetHeight },
          })
        } else {
          // 回退到原始逻辑
          const aspectRatio = originalHeight / originalWidth
          targetWidth = Math.min(containerWidth, originalWidth * 3)
          targetHeight = targetWidth * aspectRatio

          // 如果高度超过容器，按高度调整
          if (targetHeight > containerHeight) {
            targetHeight = containerHeight
            targetWidth = targetHeight / aspectRatio
          }
        }

        // 确保最小尺寸（比聊天框中的大3倍）
        const minWidth = 600 // 聊天框约200px * 3
        const minHeight = 600 // 聊天框约200px * 3

        if (targetWidth < minWidth) {
          targetWidth = minWidth
        }
        if (targetHeight < minHeight) {
          targetHeight = minHeight
        }

        console.log('弹窗目标尺寸:', { targetWidth, targetHeight })

        // 设置viewBox（如果需要）
        if (!originalViewBox && originalWidth && originalHeight) {
          svg.setAttribute('viewBox', `0 0 ${originalWidth} ${originalHeight}`)
        }

        // 强制设置SVG尺寸
        svg.style.width = `${targetWidth}px`
        svg.style.height = `${targetHeight}px`
        svg.style.maxWidth = '100%'
        svg.style.maxHeight = '100%'
        svg.style.display = 'block'
        svg.style.margin = '0 auto'

        // 移除原始尺寸属性
        svg.removeAttribute('width')
        svg.removeAttribute('height')

        // 使用slice让内容撑满（与聊天框一致）
        svg.setAttribute('preserveAspectRatio', 'xMidYMid slice')

        // 动态调整viewBox（与聊天框逻辑一致）
        try {
          const bbox = svg.getBBox()
          if (bbox.width > 0 && bbox.height > 0) {
            const padding = Math.min(bbox.width, bbox.height) * 0.1
            const newViewBox = `${bbox.x - padding} ${bbox.y - padding} ${bbox.width + padding * 2} ${bbox.height + padding * 2}`
            svg.setAttribute('viewBox', newViewBox)
            console.log('弹窗更新viewBox为:', newViewBox)
          }
        } catch (error) {
          console.log('无法获取bbox，跳过viewBox调整')
        }

        console.log('弹窗SVG尺寸调整完成')
      } catch (error) {
        console.error('调整弹窗SVG尺寸失败:', error)
      }
    },

    // 动态加载mermaid库
    loadMermaidLibrary() {
      return new Promise((resolve, reject) => {
        if (typeof window.mermaid !== 'undefined') {
          resolve()
          return
        }

        const script = document.createElement('script')
        // 使用最新的mermaid版本，支持新特性
        script.src =
          'https://cdn.jsdelivr.net/npm/mermaid@11.7.0/dist/mermaid.min.js'
        script.onload = () => {
          try {
            window.mermaid.initialize({
              startOnLoad: false,
              theme: 'default',
              securityLevel: 'loose',
              themeConfig: {
                primaryColor: '#ff6b6b',
                primaryTextColor: '#333',
                primaryBorderColor: '#ff6b6b',
                lineColor: '#333',
                secondaryColor: '#fff',
                tertiaryColor: '#fff',
              },
              // 图表配置
              flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis',
              },
              pie: {
                useMaxWidth: true,
              },
              gantt: {
                useMaxWidth: true,
              },
              journey: {
                useMaxWidth: true,
              },
              gitgraph: {
                useMaxWidth: true,
              },
              // 新增图表类型支持
              xyChart: {
                useMaxWidth: true,
                width: 700,
                height: 500,
              },
              radar: {
                useMaxWidth: true,
              },
              block: {
                useMaxWidth: true,
              },
              packet: {
                useMaxWidth: true,
              },
              kanban: {
                useMaxWidth: true,
              },
              architecture: {
                useMaxWidth: true,
              },
              mindmap: {
                useMaxWidth: true,
              },
              timeline: {
                useMaxWidth: true,
              },
              sankey: {
                useMaxWidth: true,
              },
              quadrantChart: {
                useMaxWidth: true,
              },
            })
            resolve()
          } catch (error) {
            console.error('Mermaid初始化失败:', error)
            reject(error)
          }
        }
        script.onerror = (error) => {
          console.error('Mermaid脚本加载失败:', error)
          reject(error)
        }
        document.head.appendChild(script)
      })
    },

    // 添加机器人消息
    addBotMessage(content, isHtml = false) {
      const botMessage = {
        id: this.messageIdCounter++,
        type: 'bot',
        content,
        timestamp: new Date(),
        isHtml, // 标记内容是否为HTML
      }

      // 添加到消息数组
      this.messages.push(botMessage)

      this.$nextTick(() => {
        this.scrollToBottom()

        // 如果是HTML内容且包含mermaid，触发渲染
        if (isHtml && content.includes('class="mermaid"')) {
          console.log('检测到mermaid内容，准备渲染')
          // 延迟一下再渲染，确保DOM已更新
          setTimeout(() => {
            this.renderMermaidCharts()
          }, 100)
        }
      })
    },

    // 填充示例到输入框
    fillExample(example) {
      this.inputMessage = example
      // 聚焦到文本输入框
      this.$nextTick(() => {
        const textareaInput = this.$refs.textareaInput
        if (textareaInput) {
          textareaInput.focus()
        }
      })
    },

    // 调试模拟消息
    debugSimulateMessage() {
      // 如果AI正在处理，不允许调试
      if (this.sending) {
        this.message?.warning('AI正在处理中，请等待完成后再调试')
        return
      }

      // 直接显示mermaid图表，无需用户消息
      this.sending = true

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      // 模拟AI处理延迟
      setTimeout(() => {
        const debugMermaidContent = `根据数据分析，生成以下图表：

\`\`\`mermaid
pie title Pets adopted by volunteers
    "Dogs" : 386
    "Cats" : 85
    "Rats" : 15
\`\`\`

该图表显示了志愿者收养宠物的分布情况，其中狗占大多数。`

        console.log('调试模拟消息 - 原始内容:', debugMermaidContent)

        // 处理mermaid内容并添加到消息
        const processedContent = this.parseMermaidContent(debugMermaidContent)
        console.log('调试模拟消息 - 处理后内容:', processedContent)

        this.addBotMessage(processedContent, true)
        this.sending = false
      }, 800)
    },

    // AI图表生成响应处理
    async generateDiagramResponse(userInput) {
      try {
        // 调用AI图表生成API
        const response = await this.callAIGenerateDiagramAPI(userInput)

        log('AI图表生成API响应:', response) // 调试日志

        if (response.success && response.data && response.data.text) {
          // 处理mermaid内容
          const processedContent = this.parseMermaidContent(response.data.text)
          log('处理后的mermaid内容:', processedContent) // 调试日志

          // 将处理后的内容作为机器人回复（mermaid渲染会在addBotMessage中自动处理）
          this.addBotMessage(processedContent, true)
        } else {
          this.addBotMessage('抱歉，暂时无法生成图表分析，请稍后再试。')
        }
      } catch (error) {
        console.error('AI图表生成失败:', error)
        this.addBotMessage('抱歉，图表生成服务暂时不可用，请稍后再试。')
      }
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
      })
    },

    scrollToBottom() {
      if (this.$refs.chatContent) {
        this.$refs.chatContent.scrollTop = this.$refs.chatContent.scrollHeight
      }
    },

    // 拖拽相关方法
    startDrag(event) {
      // 防止在点击按钮时触发拖拽
      if (event.target.closest('.ai-chat-actions')) {
        return
      }

      this.isDragging = true
      this.dragStartX = event.clientX - this.chatPosition.x
      this.dragStartY = event.clientY - this.chatPosition.y

      // 添加全局事件监听
      document.addEventListener('mousemove', this.onDrag, { passive: false })
      document.addEventListener('mouseup', this.stopDrag)

      // 防止文本选择和默认行为
      event.preventDefault()
      event.stopPropagation()
    },

    onDrag(event) {
      if (!this.isDragging) return

      const newX = event.clientX - this.dragStartX
      const newY = event.clientY - this.dragStartY

      // 获取对话框的实际尺寸
      const chatContainer = document.querySelector('.ai-chat-container')
      const containerWidth = chatContainer ? chatContainer.offsetWidth : 380

      const minVisibleWidth = 100 // 至少保持100px可见
      const minVisibleHeight = 50 // 至少保持50px可见

      // 计算边界 - 允许更大的拖拽范围
      const minX = -containerWidth + minVisibleWidth // 允许大部分移出左边，但保持一部分可见
      const maxX = window.innerWidth - minVisibleWidth // 允许大部分移出右边，但保持一部分可见
      const minY = -minVisibleHeight // 允许部分移出顶部
      const maxY = window.innerHeight - minVisibleHeight // 允许部分移出底部

      this.chatPosition.x = Math.max(minX, Math.min(maxX, newX))
      this.chatPosition.y = Math.max(minY, Math.min(maxY, newY))
    },

    stopDrag() {
      this.isDragging = false

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },

    // 处理mermaid弹窗事件
    handleShowMermaidModal(event) {
      const { id, code } = event.detail
      if (id && code) {
        // 创建新的mermaid图表ID
        const modalMermaidId = `mermaid-modal-${Date.now()}`
        this.mermaidModalContent = `<div id="${modalMermaidId}" class="mermaid">${code}</div>`
        this.showMermaidModal = true

        // 渲染弹窗中的mermaid图表
        this.$nextTick(() => {
          if (typeof window.mermaid !== 'undefined') {
            const element = document.getElementById(modalMermaidId)
            if (element) {
              window.mermaid.init(undefined, element)
            }
          } else {
            this.loadMermaidLibrary().then(() => {
              const element = document.getElementById(modalMermaidId)
              if (element) {
                window.mermaid.init(undefined, element)
              }
            })
          }
        })
      }
    },

    // 关闭mermaid浮层
    closeMermaidModal() {
      this.showMermaidModal = false
      this.editableMermaidCode = ''
      this.originalMermaidCode = ''
      this.mermaidSyntaxError = ''

      // 清除定时器
      if (this.mermaidRenderTimeout) {
        clearTimeout(this.mermaidRenderTimeout)
        this.mermaidRenderTimeout = null
      }
    },

    // Mermaid编辑器相关方法
    // 打开Mermaid编辑器
    async openMermaidModal(id, code, chatDimensions = null) {
      // 修复：确保代码格式正确，保留换行符，不进行额外的处理
      let formattedCode = code || ''

      // 如果代码是从URL编码中来的，先解码
      if (formattedCode && typeof formattedCode === 'string') {
        try {
          // 检查是否是URL编码的
          if (formattedCode.includes('%')) {
            formattedCode = decodeURIComponent(formattedCode)
          }
        } catch (e) {
          console.log('URL解码失败，使用原始代码')
        }

        // 只进行基本的清理，确保换行符正确
        formattedCode = formattedCode.trim()
      }

      console.log('打开Mermaid编辑器，处理后的代码:', formattedCode)
      console.log('代码是否包含换行符:', formattedCode.includes('\n'))

      this.editableMermaidCode = formattedCode
      this.originalMermaidCode = formattedCode
      this.mermaidSyntaxError = ''
      this.mermaidPreviewId = Date.now()
      this.showMermaidModal = true

      // 等待DOM更新后渲染预览
      this.$nextTick(async () => {
        await this.ensureMermaidLoaded()

        // 确保textarea正确显示多行内容
        const textarea = this.$refs.mermaidCodeEditor
        if (textarea) {
          // 强制设置值，确保换行正确显示
          textarea.value = formattedCode
          console.log('设置textarea值:', textarea.value)
          console.log('textarea行数:', textarea.value.split('\n').length)

          // 调整textarea高度以适应内容
          this.adjustTextareaHeight(textarea)

          // 确保光标定位到开始
          textarea.focus()
          textarea.setSelectionRange(0, 0)
        }

        // 延迟一下再渲染预览，确保DOM完全更新
        setTimeout(() => {
          this.renderMermaidPreview()
        }, 100)
      })
    },

    // 调整textarea高度以适应内容
    adjustTextareaHeight(textarea) {
      if (!textarea) return

      // 重置高度为auto以获取正确的scrollHeight
      textarea.style.height = 'auto'
      // 设置合适的高度，但不超过容器的最大高度
      const maxHeight = 400 // 最大高度限制
      const newHeight = Math.min(textarea.scrollHeight, maxHeight)
      textarea.style.height = newHeight + 'px'
    },

    // 确保Mermaid库已加载
    async ensureMermaidLoaded() {
      if (typeof window.mermaid === 'undefined') {
        await this.loadMermaidLibrary()
      }
    },

    // 代码变化处理
    onMermaidCodeChange() {
      // 清除之前的定时器
      if (this.mermaidRenderTimeout) {
        clearTimeout(this.mermaidRenderTimeout)
      }

      // 延迟渲染以避免频繁重渲染
      this.mermaidRenderTimeout = setTimeout(() => {
        this.renderMermaidPreview()
      }, 500)
    },

    // 渲染Mermaid预览
    async renderMermaidPreview() {
      if (!this.editableMermaidCode.trim()) {
        this.mermaidSyntaxError = ''
        return
      }

      this.mermaidRendering = true
      this.mermaidSyntaxError = ''

      try {
        await this.ensureMermaidLoaded()

        const previewElement = this.$refs.mermaidPreview
        if (!previewElement) {
          return
        }

        // 清空预览容器
        previewElement.innerHTML = ''

        // 生成唯一ID
        const graphId = `mermaid-graph-${this.mermaidPreviewId}-${Date.now()}`

        // 使用新的render API
        if (typeof window.mermaid.render === 'function') {
          const { svg } = await window.mermaid.render(
            graphId,
            this.editableMermaidCode
          )
          previewElement.innerHTML = svg
        } else {
          // 回退到旧的API
          previewElement.innerHTML = `<div class="mermaid">${this.editableMermaidCode}</div>`
          await window.mermaid.init(
            undefined,
            previewElement.querySelector('.mermaid')
          )
        }

        // 调整SVG样式
        this.adjustPreviewSvgSize(previewElement)
      } catch (error) {
        console.error('Mermaid渲染失败:', error)
        this.mermaidSyntaxError = error.message || '渲染失败'
      } finally {
        this.mermaidRendering = false
      }
    },

    // 调整预览区SVG尺寸
    adjustPreviewSvgSize(container) {
      const svg = container.querySelector('svg')
      if (!svg) return

      svg.style.width = '100%'
      svg.style.height = 'auto'
      svg.style.maxWidth = '100%'
      svg.style.display = 'block'
      svg.removeAttribute('width')
      svg.removeAttribute('height')
    },

    // 获取行数
    getLineCount() {
      return this.editableMermaidCode.split('\n').length
    },

    // 插入Tab
    insertTab(event) {
      const textarea = event.target
      const start = textarea.selectionStart
      const end = textarea.selectionEnd

      // 插入两个空格代替Tab
      const tabChar = '  '
      this.editableMermaidCode =
        this.editableMermaidCode.substring(0, start) +
        tabChar +
        this.editableMermaidCode.substring(end)

      // 恢复光标位置
      this.$nextTick(() => {
        textarea.selectionStart = textarea.selectionEnd = start + tabChar.length
      })
    },

    // 格式化代码
    formatMermaidCode() {
      // 改进的格式化：保持换行结构，只清理多余的空行
      let lines = this.editableMermaidCode.split('\n')

      // 移除开头和结尾的空行
      while (lines.length > 0 && lines[0].trim() === '') {
        lines.shift()
      }
      while (lines.length > 0 && lines[lines.length - 1].trim() === '') {
        lines.pop()
      }

      // 处理每一行：保持缩进结构，但清理多余空格
      let formatted = lines
        .map((line, index) => {
          if (line.trim() === '') return '' // 保留空行

          // 检测缩进级别
          const indent = line.match(/^(\s*)/)[1]
          const content = line.trim()

          // 根据内容类型决定缩进
          if (
            content.includes(':') &&
            !content.startsWith('title') &&
            !content.startsWith('pie')
          ) {
            // 数据行，保持4个空格缩进
            return '    ' + content
          } else {
            // 其他行保持原有缩进或不缩进
            return content
          }
        })
        .join('\n')

      this.editableMermaidCode = formatted
      this.renderMermaidPreview()
    },

    // 重置代码
    resetMermaidCode() {
      this.editableMermaidCode = this.originalMermaidCode
      this.renderMermaidPreview()
    },

    // 复制代码
    async copyMermaidCode() {
      try {
        await navigator.clipboard.writeText(this.editableMermaidCode)
        this.message?.success('代码已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.message?.error('复制失败')
      }
    },

    // 下载SVG
    downloadMermaidSvg() {
      const previewElement = this.$refs.mermaidPreview
      const svg = previewElement?.querySelector('svg')

      if (!svg) {
        this.message?.warning('没有可下载的图表')
        return
      }

      try {
        // 获取SVG内容
        const svgData = new XMLSerializer().serializeToString(svg)
        const blob = new Blob([svgData], { type: 'image/svg+xml' })

        // 创建下载链接
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `mermaid-chart-${Date.now()}.svg`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        this.message?.success('SVG文件下载成功')
      } catch (error) {
        console.error('下载失败:', error)
        this.message?.error('下载失败')
      }
    },

    // 刷新预览
    refreshMermaidPreview() {
      this.renderMermaidPreview()
    },

    // 全屏预览切换
    toggleFullscreenPreview() {
      // 实现全屏预览逻辑
      const previewContainer = document.querySelector(
        '.mermaid-preview-container'
      )
      if (!previewContainer) return

      if (!document.fullscreenElement) {
        previewContainer.requestFullscreen().catch((err) => {
          console.error('无法进入全屏模式:', err)
        })
      } else {
        document.exitFullscreen()
      }
    },
  },
}
</script>

<style scoped lang="scss">
.ai-assistant {
  font-size: 14px;
}

/* 浮动按钮样式 */
.ai-float-button {
}

.ai-float-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.ai-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 2px;
}

.ai-button-text {
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 聊天容器样式 */
.ai-chat-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 300px;
  height: calc(100vh - 40px);
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition:
    width 0.3s ease,
    opacity 0.2s ease;
  z-index: 9999;
}

/* 扩展宽度样式 */
.ai-chat-container.ai-chat-expanded {
  width: 760px;
}

/* 拖拽状态样式 */
.ai-chat-container.ai-chat-dragging {
  opacity: 0.7;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.25);
  z-index: 10001;
}

/* 聊天头部 */
.ai-chat-header {
  padding: 0 10px;
  height: 38px;
  background: #fff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: move;
  user-select: none;
  border-bottom: 1px solid #e0e3e5;
}

.ai-chat-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 14px;
}

.ai-header-icon {
  width: 17px;
  height: 13px;
  margin-right: 5px;
}

.ai-chat-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-chat-expand,
.ai-chat-debug,
.ai-chat-reset,
.ai-chat-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  color: #666;
}

.ai-chat-expand:hover,
.ai-chat-debug:hover,
.ai-chat-reset:hover,
.ai-chat-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.ai-chat-expand i,
.ai-chat-debug i,
.ai-chat-reset i,
.ai-chat-close i {
  font-size: 16px;
}

/* 聊天内容区域 */
.ai-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fff;
}

.ai-chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 消息样式 */
.ai-message {
  display: flex;
  gap: 5px;
}

.ai-message-user {
  flex-direction: row-reverse;
}

.ai-message-avatar {
  flex-shrink: 0;
  font-size: 13px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 600;
}

.ai-avatar-icon {
  width: 16px;
  height: 16px;
  color: white;
}

.ai-message-content {
  max-width: 260px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 扩展模式下的消息内容宽度 */
.ai-chat-expanded .ai-message-content {
  max-width: 660px;
}

.ai-message-text {
  padding: 10px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.4;
  word-wrap: break-word;
}

.user-select-text {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}
.ai-message-bot {
  flex-direction: column;
}
.ai-message-bot .ai-message-text {
  background: white;
  color: #333333;
  background: #f5f6f8;
}
.ai-message-bot .ai-example-text {
  background: #f4f8ff;
  border-radius: 8px;
  border: 0;
  width: 100%;
}

.ai-message-bot .ai-waiting-message {
  background-image: linear-gradient(
    269deg,
    #3e82eb 0%,
    #56adff 100%
  ) !important;
  color: white !important;
  border: none !important;
}

/* 完成消息特殊样式 */
.ai-message-completion {
  animation: completionPulse 2s ease-in-out;
}

.ai-completion-text {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  border: none !important;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  position: relative;
}

.ai-completion-text::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 20px;
  z-index: -1;
  opacity: 0.3;
  animation: completionGlow 2s ease-in-out infinite alternate;
}

.ai-message-user .ai-message-text {
  background-image: linear-gradient(269deg, #3e82eb 0%, #56adff 100%);
  color: white;
}

.ai-message-suggestion {
  padding: 8px 16px;
  background: #f3f4f6;
  border-radius: 12px;
  font-size: 13px;
  color: #6b7280;
  margin-top: 4px;
}

.ai-message-actions {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: 8px;
  margin-top: 4px;
}

.ai-message-time {
  font-size: 11px;
  color: #9ca3af;
  padding: 0 4px;
}

/* HTML内容样式 */
.ai-html-content-wrapper {
  position: relative;
}

.ai-html-content {
  line-height: 1.6;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-word;
}

.ai-html-content p {
  margin: 8px 0;
}

.ai-html-content ol,
.ai-html-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.ai-html-content li {
  margin: 4px 0;
}

.ai-html-content strong {
  font-weight: 600;
  color: #333333;
}

.ai-html-content a {
  color: #1e40af !important;
  text-decoration: underline !important;
  transition: all 0.2s;
}

.ai-html-content a:hover {
  color: #1d4ed8 !important;
  text-decoration: underline !important;
}

.ai-html-content div {
  margin: 4px 0;
}

.ai-html-content table {
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
  word-wrap: break-word;
}

.ai-html-content img {
  max-width: 100%;
  height: auto;
}

.ai-html-content pre {
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.ai-html-content code {
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 欢迎消息样式 */
.ai-welcome-text {
  margin-bottom: 12px;
  line-height: 1.5;
}

.ai-welcome-examples {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ai-example-link {
  font-size: 12px;
  color: var(--xn-color-primary);
  letter-spacing: 0;
  font-weight: 400;
}

.ai-example-link:hover {
  transform: translateY(-1px);
}

/* 输入区域 */
.ai-chat-input {
  background: white;
}

.ai-input-container {
  padding: 8px;
}

.ai-input-wrapper {
  position: relative;
  width: 100%;
}

.ai-input-textarea {
  width: 100%;
  min-height: 40px;
  max-height: 120px;
  padding: 10px 44px 24px 12px;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  resize: none;
  font-size: 14px;
  line-height: 1.4;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.ai-input-textarea:focus {
  border-color: var(--xn-color-primary);
}

.ai-input-textarea::placeholder {
  color: #9ca3af;
}

.ai-input-counter {
  position: absolute;
  bottom: 15px;
  right: 32px;
  font-size: 11px;
  color: #9ca3af;
  pointer-events: none;
  user-select: none;
  transition: color 0.2s;
}

.ai-input-counter.warning {
  color: #f59e0b;
}

.ai-input-counter.danger {
  color: #ef4444;
}

.ai-send-button {
  position: absolute;
  bottom: 10px;
  right: 5px;
  .svg-icon {
    width: 20px;
    height: 20px;
  }
  border: none;
  border-radius: 100%;
  color: var(--xn-color-primary);
}

.ai-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ai-send-button i {
  font-size: 16px;
}

.ai-waiting-message {
  background-image: linear-gradient(269deg, #3e82eb 0%, #56adff 100%);
  color: white !important;
  border: none;
  padding: 12px 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.ai-waiting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.ai-waiting-dots {
  display: flex;
  gap: 4px;
}

.ai-waiting-dots span {
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.ai-waiting-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.ai-waiting-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.ai-waiting-text {
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.ai-waiting-tip {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.ai-status-tip {
  padding: 8px 16px;
  background: #f0f9ff;
  border-top: 1px solid #e0f2fe;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #0369a1;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes completionPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes completionGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* 滚动条样式 */
.ai-chat-content::-webkit-scrollbar {
  width: 4px;
}

.ai-chat-content::-webkit-scrollbar-track {
  background: transparent;
}

.ai-chat-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.ai-chat-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 图片上传区域 */
.ai-upload-section {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: #f9fafb;
  margin-bottom: 8px;
}

.ai-upload-header {
  display: flex;
  justify-content: space-between;
  padding: 4px 12px;
}

.ai-upload-label {
  font-size: 13px;
  font-weight: 500;
  color: #333333;
  display: flex;
  flex-wrap: wrap;
}

.ai-upload-count {
  padding: 2px 6px;
  background: var(--xn-color-primary);
  color: white;
  border-radius: 10px;
  font-size: 11px;
  font-weight: normal;
}

.ai-upload-actions {
  display: flex;
  align-items: start;
  gap: 8px;
  flex-shrink: 0;
}

.ai-upload-clear {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: transparent;
  border: 1px solid #ef4444;
  border-radius: 6px;
  color: #ef4444;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.ai-upload-clear:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.ai-upload-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: var(--xn-color-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-upload-toggle:hover {
  background: var(--xn-color-primary);
  color: white;
  border-color: var(--xn-color-primary);
}

.ai-upload-content {
  padding: 12px;
  background: white;
  border-radius: 0 0 8px 8px;
}

/* 文本输入区域 */
.ai-textarea-wrapper {
  position: relative;
  width: 100%;
}

.ai-textarea {
  width: 100%;
  height: 120px;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.4;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
  font-family: inherit;
  background: white;
}

.ai-textarea:focus {
  border-color: var(--xn-color-primary);
}

.ai-textarea::placeholder {
  color: #9ca3af;
}

/* 图片上传容器样式 */
.ai-image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background: #fafafa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

/* 图片上传按钮样式 */
.ai-image-upload-button {
  width: 80px;
  height: 80px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: #ffffff;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 12px;
}

.ai-image-upload-button:hover {
  border-color: var(--xn-color-primary);
  color: var(--xn-color-primary);
  background: #f8faff;
}

.ai-image-upload-button i {
  font-size: 20px;
  margin-bottom: 4px;
}

.ai-image-upload-hint {
  font-size: 10px;
  color: #9ca3af;
  text-align: center;
  line-height: 1.2;
  margin-top: 2px;
}

/* 图片预览样式 */
.ai-image-preview {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.ai-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.ai-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ai-image-preview:hover .ai-image-overlay {
  opacity: 1;
}

.ai-image-name {
  color: white;
  font-size: 10px;
  line-height: 1.2;
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ai-image-remove {
  align-self: flex-end;
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: background 0.2s ease;
}

.ai-image-remove:hover {
  background: rgba(239, 68, 68, 1);
}

/* Mermaid图表样式 */
.mermaid-container {
  margin: 10px 0;
  padding: 15px 15px 0 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.mermaid {
  text-align: center;
  background: white;
  border-radius: 4px;
  padding: 10px;
  min-height: 200px;
  display: block;
  width: 100%;
  box-sizing: border-box;
  overflow: visible;
  margin-bottom: 10px;
}

/* Mermaid工具栏样式 */
.mermaid-toolbar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0 15px 0;
  border-top: 1px solid #e9ecef;
  margin-top: 10px;
}

.mermaid-debug-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.mermaid-debug-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.mermaid-debug-btn:active {
  transform: translateY(0);
}

.mermaid-debug-btn i {
  font-size: 14px;
}

/* mermaid SVG样式 - 确保图表撑满容器 */
.mermaid svg {
  width: 100% !important;
  height: auto !important;
  min-height: 180px !important;
  max-width: 100%;
  display: block;
  margin: 0 auto;
}

/* 针对聊天消息中的mermaid图表特殊样式 */
.ai-message-content .mermaid-container {
  width: 100%;
  max-width: none;
}

.ai-message-content .mermaid {
  min-height: 200px;
  width: 100%;
  padding: 15px;
  background: white;
  border-radius: 4px;
}

.ai-message-content .mermaid svg {
  width: 100% !important;
  height: auto !important;
  min-height: 200px !important;
  min-width: 100% !important;
  display: block !important;
  margin: 0 auto !important;
}

/* 强制Mermaid图表元素撑满 */
.ai-message-content .mermaid svg g {
  transform-origin: center center !important;
}

.ai-message-content .mermaid svg .flowchart,
.ai-message-content .mermaid svg .pie,
.ai-message-content .mermaid svg .gantt,
.ai-message-content .mermaid svg .journey {
  width: 100% !important;
  height: 100% !important;
}

/* Mermaid图表浮层样式 */
.mermaid-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

/* Mermaid编辑器弹窗样式 */
.mermaid-editor-popup {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  width: 95vw;
  height: 90vh;
  max-width: 1400px;
  max-height: 800px;
  min-width: 800px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: mermaidEditorFadeIn 0.3s ease-out;
}

/* 编辑器头部 */
.mermaid-editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8f9fa;
  flex-shrink: 0;
}

.mermaid-editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.mermaid-editor-title i {
  font-size: 20px;
  color: #3b82f6;
}

.mermaid-editor-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mermaid-action-btn,
.mermaid-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 16px;
}

.mermaid-action-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.mermaid-close-btn:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* 编辑器主体内容 */
.mermaid-editor-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

/* 左侧编辑区域 */
.mermaid-editor-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e5e7eb;
  min-width: 0;
}

/* 右侧预览区域 */
.mermaid-editor-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

/* 分割线 */
.mermaid-editor-divider {
  width: 4px;
  background: #f3f4f6;
  cursor: col-resize;
  transition: background-color 0.2s;
}

.mermaid-editor-divider:hover {
  background: #d1d5db;
}

/* 面板头部 */
.mermaid-editor-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  flex-shrink: 0;
}

.mermaid-editor-panel-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mermaid-format-btn,
.mermaid-refresh-btn,
.mermaid-fullscreen-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  color: #374151;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.mermaid-format-btn:hover,
.mermaid-refresh-btn:hover,
.mermaid-fullscreen-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* 代码编辑器区域 */
.mermaid-code-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 0;
}

.mermaid-textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  background: #fff;
  color: #1f2937;
  tab-size: 2;
}

.mermaid-textarea:focus {
  background: #fefefe;
}

/* 状态栏 */
.mermaid-editor-statusbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
  font-size: 12px;
  color: #6b7280;
  flex-shrink: 0;
}

.mermaid-char-count,
.mermaid-line-count {
  font-family: monospace;
}

.mermaid-syntax-status {
  padding: 2px 8px;
  border-radius: 12px;
  background: #dcfce7;
  color: #166534;
  font-weight: 500;
}

.mermaid-syntax-status.error {
  background: #fecaca;
  color: #dc2626;
}

/* 预览容器 */
.mermaid-preview-container {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #f8f9fa;
  min-height: 0;
}

/* 预览区域 */
.mermaid-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: white;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: auto;
}

.mermaid-preview svg {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  display: block;
}

/* 加载状态 */
.mermaid-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  gap: 12px;
}

.mermaid-loading-icon {
  font-size: 24px;
  animation: spin 1s linear infinite;
}

/* 错误状态 */
.mermaid-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #dc2626;
  gap: 12px;
  padding: 20px;
}

.mermaid-error i {
  font-size: 24px;
  flex-shrink: 0;
}

.mermaid-error-message h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.mermaid-error-message p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  opacity: 0.8;
}

/* 动画 */
@keyframes mermaidEditorFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式处理 */
@media (max-width: 1024px) {
  .mermaid-editor-popup {
    width: 98vw;
    height: 95vh;
    min-width: 600px;
  }

  .mermaid-editor-content {
    flex-direction: column;
  }

  .mermaid-editor-left {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .mermaid-editor-divider {
    display: none;
  }
}
</style>
