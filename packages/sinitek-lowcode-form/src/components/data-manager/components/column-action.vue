<template>
  <div>
    <GoDetail text="详情" :row="row" />
    <span class="mx-1 text-gray-500">|</span>
    <el-button
      title="删除"
      type="text"
      class="text-red-500"
      @click="handleDelete"
    >
      删除
    </el-button>
  </div>
</template>

<script>
import GoDetail from './go-detail.vue'
import { deleteConfirm } from './delete-data'
export default {
  name: 'ZDColumnAction',
  components: {
    GoDetail,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isShowDetail: false,
    }
  },
  methods: {
    handleDetail() {
      this.isShowDetail = true
    },
    handleDelete() {
      deleteConfirm(
        this.$createElement,
        this.row.id,
        this.formcode || this.$route.params.formcode
      )
    },
  },
}
</script>
