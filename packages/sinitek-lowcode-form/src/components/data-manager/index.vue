<template>
  <div
    v-loading="loading"
    class="data-manager h-full flex flex-col overflow-hidden rounded-lg bg-white p-4"
  >
    <!-- 工具栏 -->
    <ZDDataManagerToolbar />
    <div class="h-full overflow-hidden">
      <XTable
        ref="table"
        class="rounded-lg"
        :columns="columns"
        :border="true"
        :data="data"
        multi-sort
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
        @header-dragend="handleHeaderDragend"
      >
      </XTable>
    </div>
    <div class="mt-4 h-8 flex-shrink-0">
      <el-pagination
        class="flex justify-end"
        :current-page.sync="pageIndex"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes"
        :total="total"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import XTable from '../../zd/element-ui/table'
import { formDataService, formService } from '@/service'
import { formToColumnsAddDefaultColumn } from '../../utils/formToColumns'
import { submitFieldToModel } from '../../utils/submitFieldToModel'
import ZDDataManagerToolbar from './components/toolbar'
import ZDColumnAction from './components/column-action.vue'
import mitter from '@/utils/mitter'
import { getValueType } from '@/utils/getValueType'
import { getState, setState } from './state'

const getOrder = (sortArray) => {
  return sortArray.map((item) => ({
    orderName: item.prop,
    orderType: item.order === 'ascending' ? 'asc' : 'desc',
    valueType: getValueType(item.componentName || item.column?.componentName),
    isBaseColumn: item.isBaseColumn || item.column?.isBaseColumn,
  }))
}

export default {
  name: 'ZDDataManage',
  components: {
    XTable,
    ZDDataManagerToolbar,
  },
  provide() {
    return {
      DM: this,
    }
  },
  props: {
    formcode: {
      type: String,
      required: true,
    },
    showAction: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      data: [],
      pageIndex: 1,
      pageSize: getState(this.formcode).pageSize || 10,
      total: 0,
      columns: [],
      loading: false,
      order: [
        { prop: 'updateTimeStamp', order: 'descending', isBaseColumn: true },
      ],
      selectedRows: [],
      pageConfig: {},
      isInit: true,
      title: '表单标题',
    }
  },
  watch: {
    formcode: {
      immediate: true,
      handler(newVal) {
        this.init()
      },
    },
  },
  created() {
    mitter.on('delete-form-data', () => {
      this.getTableData()
    })
  },
  beforeDestroy() {
    mitter.off('delete-form-data')
  },
  methods: {
    async init() {
      this.loading = true
      try {
        await this.getFormData()
        await this.getTableData()
      } catch (err) {
        // 设置默认列
        this.columns = this.getAllColumns({})
        this.columns[this.columns.length - 2].width = 'auto'
      } finally {
        this.loading = false
      }
    },
    getColumns() {
      const result = this.getAllColumns(this.pageConfig)
      result.shift()
      result.pop()
      return result
    },
    getAllColumns(data) {
      const columns = formToColumnsAddDefaultColumn(data)
      const columnWidthMap = getState(this.formcode).columnWidthMap || {}
      columns.forEach((column) => {
        column.width = columnWidthMap[column.prop] || column.width
      })
      this.showAction &&
        columns.push({
          label: '操作',
          prop: 'operation',
          width: 100,
          fixed: 'right',
          align: 'center',
          renderCell: (h, { row }) => {
            return <ZDColumnAction row={row} />
          },
        })
      return columns
    },
    // 修改列函数，传入一个函数，函数参数为当前列，返回值为修改后的列
    setColumns(fn) {
      let result = fn(this.columns)
      this.columns = [...result]
      setState(this.formcode, {
        ...getState(this.formcode),
        columns: result,
      })
    },
    async getFormData() {
      await formService
        .viewForm(this.$route.params.formcode)
        .then((pageData) => {
          if (pageData) {
            this.pageConfig = Object.freeze(pageData)
            this.columns = this.getAllColumns(this.pageConfig)
          }
        })
    },
    async getTableData(params = {}) {
      this.params = { ...(this.params || {}), ...params }
      this.loading = true
      this.selectedRows = []
      if (this.isInit) {
        // 初始化时，设置搜索条件
        const searchField = getState(this.formcode).searchField
        if (searchField) {
          this.params.searchField = searchField
          this.params.logicOperator = 'AND'
        }
      }
      const orderItemList = getOrder(
        this.order.filter((item) => item.isBaseColumn)
      )
      const customOrderItemList = getOrder(
        this.order.filter((item) => !item.isBaseColumn)
      )
      return await formDataService
        .getFormDataList({
          formcode: this.formcode,
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          orderItemList: orderItemList,
          customOrderItemList: customOrderItemList,
          ...this.params,
        })
        .then((res) => {
          this.data = res.datalist.map((item) => {
            const formData = JSON.parse(item.formData || '{}')
            const model =
              submitFieldToModel(
                this.pageConfig?.children?.[0]?.children,
                formData?.submitField,
                formData?.model
              ) || {}
            model.id = item.id
            model.creatorId = item.creatorId
            model.creator = item.creator
            model.createTimeStamp = item.createTimeStamp
            model.updateTimeStamp = item.updateTimeStamp
            return model
          })
          this.total = res.totalsize
          this.$nextTick(() => {
            this.isInit && this.$emit('loaded')
            this.isInit = false
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSizeChange(size) {
      this.pageSize = size
      setState(this.formcode, {
        ...getState(this.formcode),
        pageSize: size,
      })
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.pageIndex = page
      this.getTableData()
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleSortChange(sortArray) {
      if (!sortArray || sortArray.length === 0) {
        this.order = []
      } else {
        // 将表格排序数组转换为后端接口需要的格式
        this.order = [...sortArray].map((e) => {
          return { ...e, ...e.column }
        })
      }
      this.$emit('sort-change', this.order)
      this.getTableData()
    },
    // 保存拖动后的列宽
    handleHeaderDragend(newWidth, oldWidth, column, event) {
      const state = getState(this.formcode)
      const columnWidthMap = { ...(state.columnWidthMap || {}) }
      columnWidthMap[column.property] = newWidth
      setState(this.formcode, {
        ...state,
        columnWidthMap,
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.data-manager {
  .el-pagination {
    &:before {
      display: none;
    }
  }
  ::v-deep .el-table {
    .el-table__fixed-right-patch,
    thead th {
      background: #ebeef5;
      color: #333;
    }
  }
}
</style>
