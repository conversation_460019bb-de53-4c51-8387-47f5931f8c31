<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane v-for="item in tabList" :key="item.value" :name="item.value">
      <template #label>
        <span>
          <i v-show="item.icon" :class="item.icon"></i>
          {{ item.label }}
        </span>
      </template>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
export default {
  name: 'ZDTabs',
  props: {
    tabList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeName: '',
    }
  },
  methods: {
    handleClick(v) {
      this.$emit('selectTab', v)
    },
    setActiveName(name) {
      this.activeName = name
    },
  },
}
</script>
