import form from './form/meta'
import input from './input/meta'
import number from './number/meta'
import date from './date/meta'
import dateRange from './date-range/meta'
import radio from './radio/meta'
import checkbox from './checkbox/meta'
import selectSingle from './select-single/meta'
import selectMultiple from './select-multiple/meta'
import employee from './employee/meta'
import upload from './upload/meta'
import grid from './grid/meta'
import gridItem from './grid/item-meta'
import childForm from './child-form/meta'
import page from './page/meta'

const materials = [
  page,
  input,
  number,
  form,
  date,
  dateRange,
  radio,
  checkbox,
  selectSingle,
  selectMultiple,
  employee,
  upload,
  grid,
  gridItem,
  childForm,
].map((e) => {
  e.configure.supports.supportBindState = false
  e.configure.advanced = e.configure.advanced || {}
  e.configure.advanced.callbacks = e.configure.advanced.callbacks || {}
  e.configure.advanced.callbacks.onNodeRemove =
    e.configure.advanced.callbacks.onNodeRemove ||
    function (node, target) {
      const ref = node.ref
      delete target.root.state[ref]
    }
  return e
})

export default materials
