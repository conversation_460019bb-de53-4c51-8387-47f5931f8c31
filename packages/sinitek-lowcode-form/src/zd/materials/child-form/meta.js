import * as Constants from '../../common/enum.js'
import icon from './icon.svg'
/**
 * @TODO 分页，新增数据，操作列功能，只读功能
 */
export default {
  componentName: 'ZDChildForm',
  title: '子表单',
  category: '常用',
  treeKey: 'label',
  props: [
    {
      name: 'label',
      title: '标题',
      setter: 'StringSetter',
    },
    {
      name: 'state',
      title: '状态',
      defaultValue: Constants.FormState.NORMAL,
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: Constants.FormStateOptions.filter(
            (item) => item.value !== Constants.FormState.DISABLED
          ),
        },
      },
    },
    {
      type: 'group',
      title: '子表单全局设置',
      items: [
        {
          name: 'addBtnText',
          title: '按钮名称',
          defaultValue: Constants.ChildrenForm.Text.AddBtn,
          setter: 'StringSetter',
        },
        {
          name: 'addBtnState',
          title: '按钮状态',
          defaultValue: Constants.FormState.NORMAL,
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: Constants.FormStateOptions.filter(
                (item) => item.value !== Constants.FormState.READONLY
              ),
            },
          },
        },
      ],
    },
    {
      type: 'group',
      title: '操作列设置',
      items: [
        {
          name: 'showAction',
          title: '显示操作列',
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'showDeleteBtn',
          title: '显示删除',
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'deleteBtnText',
          title: '删除文本',
          defaultValue: Constants.ChildrenForm.Text.DeleteBtn,
          setter: 'StringSetter',
        },
        {
          name: 'deleteBtnConfirm',
          title: '删除确认',
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'showCopyBtn',
          title: '显示复制',
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'copyText',
          title: '复制文本',
          defaultValue: Constants.ChildrenForm.Text.CopyBtn,
          setter: 'StringSetter',
          condition: (props) => props.showCopyBtn,
        },
        {
          name: 'showSortBtn',
          title: '显示排序',
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'moveUp',
          title: '上移文本',
          defaultValue: Constants.ChildrenForm.Text.MoveUp,
          setter: 'StringSetter',
          condition: (props) => props.showSortBtn,
        },
        {
          name: 'moveDown',
          title: '下移文本',
          defaultValue: Constants.ChildrenForm.Text.MoveDown,
          setter: 'StringSetter',
          condition: (props) => props.showSortBtn,
        },
      ],
    },

    {
      type: 'group',
      title: '展示样式',
      items: [
        // {
        //   name: 'usePlatform',
        //   title: '使用desktop/mobile端设置',
        //   defaultValue: 'desktop',
        //   setter: {
        //     componentName: 'RadioGroupSetter',
        //     props: {
        //       options: [
        //         { label: 'Desktop', value: 'desktop' },
        //         { label: 'Mobile', value: 'mobile' },
        //       ],
        //     },
        //   },
        // },
        {
          name: 'dTheme',
          title: '表格主题',
          defaultValue: Constants.ChildrenForm.Theme.Split,
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: Constants.ChildrenForm.Theme.Items,
            },
          },
        },
        {
          name: 'dShowHeader',
          title: '显示表头',
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'dShowNo',
          title: '显示序号',
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'dPageSize',
          title: '分页条数',
          defaultValue: 20,
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                { label: '5条/页', value: 5 },
                { label: '10条/页', value: 10 },
                { label: '20条/页', value: 20 },
                { label: '30条/页', value: 30 },
              ],
            },
          },
        },
        {
          name: 'dMaxItems',
          title: '最大条数',
          defaultValue: 50,
          setter: 'NumberSetter',
        },
        {
          name: 'dFixedLeft',
          title: '冻结左侧',
          defaultValue: 0,
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: [
                { label: '无', value: 0 },
                { label: '1列', value: 1 },
                { label: '2列', value: 2 },
                { label: '3列', value: 3 },
              ],
            },
          },
        },
        {
          name: 'dFixedAction',
          title: '冻结操作列',
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'dActionWidth',
          title: '操作列宽度',
          defaultValue: 150,
          setter: 'NumberSetter',
        },
        {
          name: 'dCustomWidth',
          title: '自定义其他列宽度',
          defaultValue: false,
          setter: 'BoolSetter',
        },
      ],
    },
    // {
    //   title: '校验规则',
    //   type: 'group',
    //   items: [
    //     {
    //       name: 'rules',
    //       setter: {
    //         componentName: 'RuleSetter',
    //         props: {
    //           items: [
    //             {
    //               label: '必填',
    //               key: 'required',
    //               type: 'boolean',
    //             },
    //           ],
    //         },
    //       },
    //     },
    //   ],
    // },
    // {
    //   name: 'rules',
    //   title: '校验规则',
    //   setter: 'ArraySetter',
    // },
  ],
  snippets: [
    {
      title: '子表单',
      screenshot: icon,
      schema: {
        componentName: 'ZDChildForm',
        props: {
          label: '子表单',
          clearable: true,
          state: Constants.FormState.NORMAL,
          maxLength: 200,
          clear: true,
          rules: [],
        },
        children: [],
      },
    },
  ],
  configure: {
    component: {
      isContainer: true,
      nestingRule: {
        childBlacklist: ['ZDChildForm', 'ZDFlex'],
      },
    },
    supports: {
      condition: false,
      style: false,
    },
  },
}
