<template>
  <div class="ml-auto f-c-c">
    <!-- 切换模拟器大小 -->
    <div class="i-material-symbols-light:desktop-windows-outline hidden"></div>
    <div
      class="i-material-symbols-light:phone-android-outline-rounded hidden"
    ></div>
    <ZDIcon
      v-for="(item, i) of platformList"
      v-bind="item"
      :key="i"
      :class="{ 'bg-#F5F5F5 text-primary': platform === item.key }"
      :size="30"
      @click="changePlatform(item)"
    />
  </div>
</template>

<script>
import ZDIcon from '../../../common/icon'
export default {
  name: 'ZDDevice',
  components: {
    ZDIcon,
  },
  inject: ['$doc'],
  data() {
    return {
      platformList: [
        {
          icon: 'i-material-symbols-light:desktop-windows-outline',
          float: '电脑端',
          key: 'desktop',
        },
        {
          icon: 'i-material-symbols-light:phone-android-outline-rounded',
          float: '手机端',
          key: 'phone',
          disabled: true,
        },
      ],
      platform: 'desktop',
    }
  },
  methods: {
    changePlatform(item) {
      if (item.disabled) return
      this.platform = item.key
      this.$doc.container.setDevice(item.key)
    },
  },
}
</script>
