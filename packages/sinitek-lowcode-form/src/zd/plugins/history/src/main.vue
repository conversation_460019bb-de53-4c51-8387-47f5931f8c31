<template>
  <div class="flex flex-shrink-0 border-l border-#efefef ml-1 pl-2">
    <div
      class="h-7 w-7"
      :class="[![3, 4].includes(state) ? 'icon-disabled' : 'icon-hover']"
    >
      <div
        class="i-material-symbols-light:undo?mask h-7 w-7"
        :float="![3, 4].includes(state) ? '没有要撤销的' : '撤销'"
        @click="undo"
      ></div>
    </div>
    <div
      class="ml-1 h-7 w-7"
      :class="[![1, 4].includes(state) ? 'icon-disabled' : 'icon-hover']"
    >
      <div
        class="i-material-symbols-light:redo?mask h-7 w-7"
        :float="![1, 4].includes(state) ? '没有要恢复的' : '恢复'"
        @click="redo"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ZDHistory',
  inject: ['$doc'],
  data() {
    return {
      state: 0,
    }
  },
  mounted() {
    this.$doc.history.onChange((state) => {
      this.state = state
    })
  },
  methods: {
    redo() {
      this.$doc.history.forward()
    },
    undo() {
      this.$doc.history.back()
    },
  },
}
</script>
