<template>
  <el-button
    v-loading.fullscreen.lock="isLoading"
    :disabled="isLoading"
    size="mini"
    type="primary"
    class="mx-2"
    @click="onSave"
    >保存</el-button
  >
</template>

<script>
import { formService } from '@/service'
export default {
  name: 'ZDTopSave',
  inject: ['$doc'],
  data() {
    return {
      isLoading: false,
    }
  },
  mounted() {
    // 页面离开时判断
    window.onbeforeunload = () => {
      const hasChange = this.$doc.checkChange()
      if (hasChange) {
        event.preventDefault()
        // // Chrome requires returnValue to be set.
        event.returnValue = ''
      }
    }
  },
  destroyed() {
    window.onbeforeunload = null
  },
  methods: {
    onSave() {
      this.isLoading = true
      formService
        .saveOrUpdateForm({
          pageData: this.$doc.getSchema(true),
          code: this.$route.params.formcode,
        })
        .then(() => {
          this.$message.success('保存成功')
        })
        .finally(() => {
          this.isLoading = false
          this.$doc.resetOriginal()
        })
    },
  },
}
</script>
