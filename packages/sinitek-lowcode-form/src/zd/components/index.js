import { isDesktop } from 'sinitek-lowcode-shared'
import Form from './form'
import Input from './input'
import Number from './number'
import Date from './date'
import DateRange from './date-range'
import Radio from './radio'
import Checkbox from './checkbox'
import SelectSingle from './select-single'
import SelectMultiple from './select-multiple'
import Employee from './employee'
import Upload from './upload'
import Grid from './grid'
import GridItem from './grid/item'
import ChildForm from './child-form'
import Page from './page'
const pcComponents = {
  [Input.name]: Input,
  [Number.name]: Number,
  [Form.name]: Form,
  [Date.name]: Date,
  [DateRange.name]: DateRange,
  [Radio.name]: Radio,
  [Checkbox.name]: Checkbox,
  [SelectSingle.name]: SelectSingle,
  [SelectMultiple.name]: SelectMultiple,
  [Employee.name]: Employee,
  [Upload.name]: Upload,
  [Grid.name]: Grid,
  [GridItem.name]: GridItem,
  [ChildForm.name]: ChildForm,
  [Page.name]: Page,
}
// const mobileComponents = {}

const exportComponents = isDesktop ? pcComponents : pcComponents

export default exportComponents
