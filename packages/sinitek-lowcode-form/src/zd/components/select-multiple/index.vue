<template>
  <ZDFormItem
    :value="currentLabel"
    :label="label"
    :state="state"
    :tips="tips"
    :rules="compRules"
    :prop="prop"
  >
    <template v-if="currentLabel.length && useColor" #readonly>
      <ZDTag
        v-for="(item, i) of currentLabel"
        :key="i"
        class="mr-2"
        :item="item"
      />
    </template>
    <XSelect
      v-model="currentValue"
      class="w-full"
      :list="list"
      :disabled="isDisabled"
      :use-color="useColor"
      :clearable="clear"
      :no-match-text="emptyText"
      :no-data-text="emptyText"
      :filterable="search"
      :multiple="true"
      :use-select-all="useSelectAll"
      @change="handleChange"
    >
    </XSelect>
  </ZDFormItem>
</template>

<script>
import { FormState, FormStateValues } from '../../common/enum'
import stateMixin from '../../mixins/state'
import ZDFormItem from '../../common/form-item.vue'
import ZDTag from '../../common/tag.vue'
import XSelect from '../../element-ui/select/select.vue'
export default {
  name: 'ZDSelectMultiple',
  components: {
    ZDFormItem,
    ZDTag,
    XSelect,
  },
  mixins: [stateMixin],
  props: {
    value: null,
    defaultValue: Array,
    label: String,
    prop: String,
    tips: String,
    state: {
      type: String,
      default: FormState.NORMAL,
      validator: (value) => FormStateValues.includes(value),
    },
    useSelectAll: {
      type: Boolean,
      default: false,
    },
    useColor: {
      type: Boolean,
      default: false,
    },
    clear: {
      type: Boolean,
      default: true,
    },
    search: {
      type: Boolean,
      default: true,
    },
    emptyText: {
      type: String,
      default: '无数据',
    },
    optionsType: {
      type: String,
      default: 'custom',
      validator: (value) => ['custom'].includes(value),
    },
    options: {
      type: Array,
      default: () => [],
    },
    rules: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      currentValue: this.value || this.defaultValue || [],
      isArrayRule: true,
    }
  },
  computed: {
    list() {
      if (this.optionsType === 'custom') {
        return this.options
      }
      return []
    },
    currentLabel() {
      return this.list.filter((item) => this.currentValue.includes(item.value))
    },
  },
  watch: {
    value: {
      handler(val) {
        if (!val || val.length === 0) return
        const keys = val.reduce((acc, item) => {
          if (!item) return acc
          const [key] = item.split(':')
          acc.push(key)
          return acc
        }, [])
        this.currentValue = keys
      },
      immediate: true,
    },
    // @TODO 在预览时，如果设置了默认值，在去修改值时，这个watch会触发很奇怪, 目前是比对2次的值不进行修改
    defaultValue(v, o) {
      // 在设计时
      if (!v || JSON.stringify(v) === JSON.stringify(o)) return
      this.currentValue = v
    },
  },
  mounted() {
    // 当有默认值时，设置当前值
    // 通过getPageCtx要可以获取到值，所有默认就得给他设置一下值
    if (this.defaultValue) {
      this.currentValue = this.defaultValue
      this.handleChange(this.currentValue)
    }
  },
  methods: {
    getValues(val) {
      return val.reduce((acc, item) => {
        const [key] = item.split(':')
        const option = this.list.find((item) => item.value === key)
        acc.push(`${option.value}:${option.label}`)
        return acc
      }, [])
    },
    handleChange(v) {
      this.$emit('input', this.getValues(v))
    },
  },
}
</script>
