<template>
  <ZDFormItem
    :value="currentValue"
    :label="label"
    :state="state"
    :tips="tips"
    :rules="compRules"
    :prop="prop"
  >
    <ZDInputNumber
      v-if="!isReadonly"
      v-model="currentValue"
      class="w-full"
      :placeholder="placeholder"
      :disabled="isDisabled"
      controls-position="right"
      :inner-after="innerAfter"
    />
  </ZDFormItem>
</template>

<script>
import { FormState, FormStateValues } from '../../common/enum'
import stateMixin from '../../mixins/state'
import baseMixin from '../../mixins/base'
import ZDFormItem from '../../common/form-item.vue'
import ZDInputNumber from '../../element-ui/input-number.vue'
export default {
  name: 'ZDNumber',
  components: {
    ZDFormItem,
    ZDInputNumber,
  },
  mixins: [stateMixin, baseMixin],
  props: {
    value: null,
    label: String,
    prop: String,
    placeholder: {
      type: String,
      default: '请输入数值',
    },
    tips: {
      type: String,
      default: '',
    },
    state: {
      type: String,
      default: FormState.NORMAL,
      validator: (value) => FormStateValues.includes(value),
    },
    innerAfter: {
      type: String,
      default: '',
    },
    defaultValue: {
      type: Object,
      default: () => {},
    },
    maxLength: {
      type: Number,
      default: 200,
    },
    rules: {
      type: Array,
      default: () => [],
    },
  },
}
</script>
