<template>
  <ZDFormItem
    :value="currentValue"
    :label="label"
    :state="state"
    :tips="tips"
    :rules="compRules"
    :prop="prop"
  >
    <el-date-picker
      v-if="!isReadonly"
      class="w-full"
      :value="currentValue"
      :placeholder="placeholder"
      :disabled="isDisabled"
      :type="type"
      :default-time="defaultTime"
      :format="formatter"
      :picker-options="pickerOptions"
      :value-format="valueFormat"
      @focus="onFocus"
      @input="onInput"
    />
  </ZDFormItem>
</template>

<script>
import { FormState, FormStateValues } from '../../common/enum'
import stateMixin from '../../mixins/state'
import baseMixin from '../../mixins/base'
import ZDFormItem from '../../common/form-item.vue'
import moment from 'moment'
export default {
  name: 'ZDDate',
  components: {
    ZDFormItem,
  },
  mixins: [stateMixin, baseMixin],
  props: {
    value: null,
    label: String,
    prop: String,
    placeholder: {
      type: String,
      default: '',
    },
    tips: {
      type: String,
      default: '',
    },
    state: {
      type: String,
      default: FormState.NORMAL,
      validator: (value) => FormStateValues.includes(value),
    },
    clear: {
      type: Boolean,
      default: true,
    },
    defaultValue: {
      type: Object,
      default: () => {},
    },
    formatter: {
      type: String,
      default: 'yyyy-MM-dd',
    },
    rangeType: {
      type: String,
      default: 'normal',
      validator: (value) =>
        ['normal', 'beforeToday', 'afterToday', 'disabledRange'].includes(
          value
        ),
    },
    disabledBegin: {
      type: String,
      default: '',
    },
    disabledEnd: {
      type: String,
      default: '',
    },
    rules: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      defaultTime: '',
    }
  },
  computed: {
    type() {
      if (this.formatter == 'yyyy') {
        return 'year'
      } else if (this.formatter === 'yyyy-MM') {
        return 'month'
      } else if (this.formatter === 'yyyy-MM-dd') {
        return 'date'
      }
      return 'datetime'
    },
    valueFormat() {
      return this.formatter
    },
    // 给base mixin 使用
    format() {
      const res = this.formatter.replace(/yyyy|MM|dd|HH|mm|ss/g, (match) => {
        if (match === 'yyyy') {
          return 'YYYY'
        } else if (match === 'dd') {
          return 'DD'
        }
        return match
      })
      return res
    },
    pickerOptions() {
      return {
        disabledDate: (time) => {
          if (this.rangeType === 'beforeToday') {
            return time.getTime() > Date.now()
          } else if (this.rangeType === 'afterToday') {
            return time.getTime() < Date.now()
          } else if (this.rangeType === 'disabledRange') {
            return (
              time.getTime() >
                moment(this.disabledBegin).add(-1, 'day').toDate().getTime() &&
              time.getTime() < moment(this.disabledEnd).toDate().getTime()
            )
          }
          return false
        },
      }
    },
  },
  methods: {
    onFocus() {
      this.defaultTime = moment().format('HH:mm:ss')
    },
    onInput(value) {
      this.currentValue = value
      this.$emit('input', value)
    },
  },
}
</script>
