<template>
  <div>
    <Popover v-model="show" :el="$refs.reference" :offset="10">
      <div class="fs-12">
        <!-- <div v-for="item in items" :key="item.key">
            
          </div> -->
        <FieldItem v-if="item.key !== 'required'" :label="item.label">
          <component
            :is="getComponent(item.type)"
            v-model="currentValue[item.key]"
            size="mini"
            @change="handleChange"
          />
        </FieldItem>
        <FieldItem label="启用">
          <el-checkbox v-model="currentValue.enable" @change="handleChange" />
        </FieldItem>
        <FieldItem label="错误信息">
          <el-input
            v-model="currentValue.errorMessage"
            @change="handleChange"
          />
        </FieldItem>
      </div>
    </Popover>
    <div ref="reference" class="flex items-center justify-between fs-12">
      <span>{{ item.label }}</span>
      <div class="right">
        <i
          class="el-icon-edit-outline mr-1 cursor-pointer"
          float="编辑"
          @click="show = true"
        />
        <el-checkbox v-model="currentValue.enable" @change="handleChange" />
      </div>
    </div>
  </div>
</template>

<script>
/**
 * label   icon checkbox
 * 点击icon时左边展示属性设置
 * @example
 * {
 *  componentName: 'RuleSetter',
 *  props: {
 *    items: [
 *      // 默认有启用和错误提示2个字段
 *      {
 *        label: '最小值',
 *        key: 'min',
 *        type: 'number',
 *      },
 *  }
 * }
 */
import { createComponent } from 'sinitek-lowcode-simulator'
import Popover from '../../common/popover.vue'
import FieldItem from '../../common/field-item.vue'
export default createComponent({
  name: 'RuleSetterItem',
  components: {
    Popover,
    FieldItem,
  },
  props: ['item', 'label'],
  data() {
    return {
      currentValue: this?.modelValue ?? {
        enable: false,
        [this.item.key]: '',
        errorMessage: '',
      },
      show: false,
    }
  },
  methods: {
    getComponent(type) {
      switch (type) {
        case 'number':
          return 'el-input-number'
        case 'string':
          return 'el-input'
        case 'boolean':
          return 'el-switch'
        default:
          return 'el-input'
      }
    },
    handleChange() {
      this._emit({ ...this.currentValue, key: this.item.key })
    },
  },
  watch: {
    modelValue(v) {
      this.currentValue = v ?? {
        enable: false,
        [this.item.key]: '',
        errorMessage: '',
      }
    },
  },
})
</script>
