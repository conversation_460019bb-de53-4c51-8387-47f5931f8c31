<template>
  <div>
    <el-button class="w-full" size="mini" @click="onSet">设置</el-button>
    <xn-employee
      ref="emp"
      multi
      :value="currentValue"
      :mode="mode"
      class="absolute left-9999"
      @getResult="getResult"
    />
  </div>
</template>

<script>
import { createComponent } from 'sinitek-lowcode-simulator'
import XnEmployee from '@/components/employee'
export default createComponent({
  name: 'EmployeeSetter',
  components: {
    XnEmployee,
  },
  props: ['defaultValue', 'mode'],
  data() {
    return {
      defaultTime: '',
      isMounted: false,
    }
  },
  computed: {
    currentValue() {
      const v = this.modelValue ?? this.defaultValue
      if (!v?.length) return ''
      return typeof v === 'string' ? v : v.join(',')
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.isMounted = true
    })
  },
  methods: {
    onSet() {
      this.$refs.emp.handleIconClick()
    },
    getResult(v) {
      if (!this.isMounted) {
        return
      }
      const ids = v.linkOrg.map((e) => e.id).join(',')
      this._emit(ids)
    },
  },
})
</script>
