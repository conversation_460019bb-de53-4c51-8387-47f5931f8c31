<template>
  <div>
    <Popover
      v-model="show"
      :el="$refs.reference"
      :offset="10"
      @hook:mounted="onPopoverMounted"
    >
      <div>
        <FieldItem v-show="!item.isOther" label="显示值">
          <el-input v-model="currentItem.label" @input="labelChange" />
        </FieldItem>

        <FieldItem v-show="!item.isOther" label="选项值">
          <el-input :value="currentItem.value" @input="valueInput">
            <i
              slot="suffix"
              :float="
                currentItem.lock
                  ? '点击解锁可以单独设置值'
                  : '点击锁定可以统一设置值'
              "
              :class="[
                'el-input__icon',
                !currentItem.lock ? 'el-icon-unlock' : 'el-icon-lock',
              ]"
              @click="toggleLock"
            ></i>
          </el-input>
        </FieldItem>
        <FieldItem v-show="!item.isOther" label="默认选中">
          <el-switch :value="checked" @change="switchChange" />
        </FieldItem>
        <FieldItem v-if="useColor" label="颜色">
          <ZDColor v-model="currentItem.color" @input="colorChange" />
        </FieldItem>
      </div>
    </Popover>
    <div
      ref="reference"
      class="flex items-center py-1 justify-between b rounded-sm hover:bg-gray-100 transition-colors option-item"
    >
      <div class="left flex items-center" :class="{ 'pl-2': item.isOther }">
        <div
          v-if="!item.isOther"
          class="drop-handler i-material-symbols-light:more-vert h-6 w-6 opacity-50 important-cursor-move hover:opacity-100 transition-opacity flex-shrink-0"
        ></div>
        <el-radio
          v-if="isRadio"
          :value="currentValue"
          :label="item.value"
          @click.native="handleChange($event, item)"
        >
          <span>{{ item.label }}</span>
        </el-radio>
        <el-checkbox
          v-else
          v-model="currentValue"
          :label="item.value"
          @input="checkInput"
        >
          <span>{{ item.label }}</span>
        </el-checkbox>
      </div>
      <div class="right pr-2">
        <i
          v-if="!item.hideEdit"
          class="el-icon-edit-outline mr-2 cursor-pointer"
          float="编辑"
          @click="show = true"
        />
        <i
          class="el-icon-delete cursor-pointer"
          float="删除"
          @click="onRemove"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Popover from '../common/popover.vue'
import FieldItem from '../common/field-item.vue'
import ZDColor from '../common/color.vue'
import { setObserver } from 'sinitek-lowcode-shared'
const fillProp = (item) => {
  const result = item
  if (result.lock === void 0) {
    result.lock = true
  }
  if (result.color === void 0) {
    result.color = null
  }
  if (result.hideEdit === void 0) {
    result.hideEdit = false
  }
  return result
}

export default {
  name: 'OptionItem',
  components: {
    Popover,
    FieldItem,
    ZDColor,
  },
  inject: ['$doc'],
  props: {
    useColor: {
      type: Boolean,
      default: false,
    },
    isRadio: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: () => {},
    },
    value: {
      type: null,
    },
    index: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      currentItem: fillProp(this.item),
      show: false,
      currentValue: this.value,
    }
  },
  computed: {
    checked() {
      if (Array.isArray(this.currentValue)) {
        return this.currentValue.includes(this.currentItem.value)
      }
      return this.currentValue === this.currentItem.value
    },
  },
  watch: {
    value(v) {
      this.currentValue = v
    },
    item: {
      handler() {
        this.currentItem = fillProp(this.item)
      },
      deep: true,
    },
  },
  methods: {
    onPopoverMounted() {
      if (this.currentItem.open) {
        setTimeout(() => {
          this.show = true
          this.currentItem.open = false
        })
      }
    },
    onChange() {
      this.$emit('change', {
        item: this.currentItem,
        index: this.index,
      })
    },
    checkInput(v) {
      setObserver(this.$doc.getCurrent().schema.props, 'defaultValue', v)
      this.$emit('input', v)
    },
    valueInput(v) {
      if (!this.currentItem.lock) {
        this.currentItem.value = v
      }
      this.onChange()
    },
    onRemove() {
      this.$emit('remove', this.index)
    },
    labelChange(v) {
      if (this.currentItem.lock) {
        this.currentItem.value = v
      }
      this.onChange(v)
    },
    toggleLock() {
      this.currentItem.lock = !this.currentItem.lock
      if (this.currentItem.lock) {
        this.currentItem.value = this.currentItem.label
        this.onChange()
      }
    },
    switchChange(v) {
      if (v) {
        this.checkInput(this.currentItem.value)
      } else {
        if (Array.isArray(this.currentValue)) {
          const newArr = [...this.currentValue]
          const index = newArr.indexOf(this.currentItem.value)
          if (index !== -1) {
            newArr.splice(index, 1)
          }
          this.checkInput(newArr)
        } else {
          this.checkInput('')
        }
      }
    },
    colorChange(v) {
      this.currentItem.color = v
      this.onChange()
    },
    handleChange(event, v) {
      if (event.pointerId <= 0) return
      if (this.isRadio) {
        this.currentValue = this.currentValue === v.value ? '' : v.value
        this.checkInput(this.currentValue)
      }
    },
  },
}
</script>
