<template>
  <div>
    <el-popover
      placement="bottom"
      width="250"
      trigger="click"
      @after-enter="getSearchSuggest"
    >
      <div class="search-suggest-container">
        <el-input
          ref="searchRef"
          v-model="filterText"
          placeholder="搜索页面"
          clearable
          size="mini"
          class="mb-2"
          @input="searchQuery"
          @clear="clear"
        >
        </el-input>
        <el-tree
          ref="tree"
          v-loading="loading"
          node-key="code"
          :data="treeData"
          :props="defaultProps"
          default-expand-all
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          class="group-tree"
          @node-click="handleNodeClick"
        >
          <div
            slot-scope="{ node, data }"
            class="label-container h-8 flex items-center fs-14"
            :class="data.pageType === 0 ? 'enable-node' : 'disabled-node'"
          >
            <div :id="data.id" class="label-text">
              {{ node.label }}
            </div>
          </div>
        </el-tree>
      </div>
      <i slot="reference" class="el-icon-caret-bottom popup-icon"></i>
    </el-popover>
  </div>
</template>
<script>
import { getPath } from '@/routes/index'
import { groupService } from '@/service'
export default {
  name: 'ZDNavSearchPopup',
  data() {
    return {
      treeData: [],
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      loading: false,
    }
  },
  mounted() {},
  methods: {
    getTree() {
      this.loading = true
      groupService
        .fetchAppTreeData({
          appcode: this.$route.params.appcode,
        })
        .then((data) => {
          const result = []
          data.forEach((e) => {
            if (e.pageType === 0) {
              result.push(e)
            } else if (e.children?.length) {
              let v = []
              e.children.forEach((child) => {
                // 如果子节点是页面，则添加到结果中
                if (child.pageType === 0 || child.children?.length) {
                  v.push(child)
                  return
                }
              })
              if (v.length) {
                result.push({ ...e, children: v })
              }
            }
          })
          this.treeData = result
          this.$nextTick(() => {
            this.loading = false
          })
        })
        .catch(() => {
          this.loading = false
        })
    },
    checkOverflow() {
      document.querySelectorAll('.label-text').forEach((el) => {
        const isOverflow = el.scrollWidth > el.clientWidth
        el.title = isOverflow ? el.innerText : ''
      })
    },
    getSearchSuggest() {
      this.filterText = ''
      if (this.$refs.tree) {
        this.setTreeNodeStyle()
        this.$nextTick(() => {
          this.checkOverflow()
        })
      }
    },
    setTreeNodeStyle() {
      this.$nextTick(() => {
        const disabledNodes = document.getElementsByClassName('disabled-node')
        for (let i = 0; i < disabledNodes.length; i++) {
          disabledNodes[i].parentElement.style.cursor = 'default'
          disabledNodes[i].parentElement.style.setProperty(
            'background-color',
            'inherit',
            'important'
          )
          disabledNodes[i].parentElement.style.setProperty(
            'color',
            'inherit',
            'important'
          )
          disabledNodes[i].style.setProperty('font-weight', '400', 'important')
        }
        const enabledNodes = document.getElementsByClassName('enable-node')
        for (let i = 0; i < enabledNodes.length; i++) {
          enabledNodes[i].parentElement.style.cursor = 'pointer'
          enabledNodes[i].parentElement.parentElement.style.pointerEvents =
            'auto'
        }
      })
    },
    searchQuery(val) {
      this.$refs.tree.filter(val)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleNodeClick(data) {
      if (data.pageType !== 0) {
        return
      }
      this.filterText = data.name
      if (data.code !== this.$route.params.formcode) {
        this.$router.push(
          getPath({
            appcode: this.$route.params.appcode,
            type: 'design',
            formcode: data.code,
          })
        )
      }
    },
    clear() {
      this.$refs.searchRef.blur()
    },
  },
}
</script>
<style scoped lang="scss">
.popup-icon {
  color: #606266;
  font-size: 16px;
  cursor: pointer;
  border: 1px solid #606266;
  border-radius: 3px;
  margin-left: -5px;
}
.popup-icon:hover {
  background-color: #ebeef5;
}
.group-tree {
  max-height: 300px !important;
  overflow-y: auto !important;
  ::v-deep
    .el-tree-node
    .el-tree-node__content:has(+ .el-tree-node__children:empty) {
    .el-tree-node__expand-icon {
      display: none;
    }
    .label-container {
      @apply pl-1;
    }
  }
}
.label-container {
  width: 100%;
  min-width: 0;
}

.label-text {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 保留展开图标功能 */
::v-deep .el-tree-node__expand-icon {
  pointer-events: auto !important;
}

::v-deep .el-tree-node__content {
  font-size: 16px !important;
}

::v-deep .el-loading-spinner {
  display: flex;
  justify-content: center;
}
</style>
