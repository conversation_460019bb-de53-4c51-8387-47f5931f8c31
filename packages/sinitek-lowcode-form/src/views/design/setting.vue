<template>
  <el-container class="h-full">
    <SettingMenu
      :menu-data="menuData"
      @handleSelectMenu="handleSelectMenu"
    ></SettingMenu>
    <el-main class="p-0">
      <router-view :key="$route.fullPath"></router-view>
    </el-main>
  </el-container>
</template>

<script>
import { getPath } from '@/routes/index'
import SettingMenu from '@/components/menu/menu.vue'
export default {
  name: 'ZDDesignSetting',
  components: {
    SettingMenu,
  },
  data() {
    return {
      menuData: [
        {
          id: 'basicSetting',
          label: '基础设置',
          formcode: 'basicSetting',
        },
        {
          id: 'rightSetting',
          label: '权限设置',
          formcode: 'rightSetting',
        },
        {
          id: 'sceneSetting',
          label: '场景设置',
          formcode: 'sceneSetting',
        },
      ],
    }
  },
  methods: {
    handleSelectMenu(key) {
      if (this.$route.fullPath.indexOf(key) === -1) {
        this.$router.push(
          getPath({
            appcode: this.$route.params.appcode,
            type: 'design',
            formcode: this.$route.params.formcode,
            suffix: `setting/${key}`,
          })
        )
      }
    },
  },
}
</script>
