<template>
  <div
    v-loading="loading"
    class="h-full flex flex-col overflow-hidden"
    :class="`w-${$route.query.w}`"
  >
    <ZDWorkBenchNav v-if="!$route.query.hideNav"></ZDWorkBenchNav>
    <ZDDetailRender
      ref="render"
      :formcode="$route.params.formcode"
      :config="config"
      :fetcher="fetcher"
    />
    <!-- 操作按钮 -->
    <div
      class="z-2 flex flex-shrink-0 justify-center bg-white p-4 shadow-[0_2px_12px_0_rgba(0,0,0,0.1)]"
    >
      <template v-if="!isEdit">
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="handleDelete">删除</el-button>
      </template>
      <template v-else>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </div>
  </div>
</template>

<script>
import ZDDetailRender from './components/render.vue'
import { formDataService } from '@/service'
import { FormState } from '@/zd/common/enum'
import { deleteConfirm } from '../../components/data-manager/components/delete-data'
import { getUrl } from '@/utils/getUrl'
import formMixin from '@/mixins/form'
import mitter from '@/utils/mitter'
import { JSFunction } from 'sinitek-lowcode-shared'
import { submitFieldToModel } from '@/utils/submitFieldToModel'
export default {
  name: 'ZDFormDetail',
  components: {
    ZDDetailRender,
    ZDWorkBenchNav: () => import('../workbench/components/nav.vue'),
  },
  mixins: [formMixin],
  data() {
    return {
      config: {},
      loading: false,
      isEdit: false,
    }
  },
  created() {
    this.getFormData()
  },
  methods: {
    async getFormData() {
      this.loading = true
      try {
        const id = this.$route.query.id
        const res = await formDataService.dataView({
          id,
          formcode: this.$route.params.formcode,
        })
        if (!res.id) {
          // 跳转空页面
          location.replace(getUrl('/status/no-data'))
          return
        }
        const config = await this.getPageConfig()
        if (!config) {
          this.$message.error('表单不存在')
          return
        }
        config.state.model = submitFieldToModel(
          config.children[0].children,
          res.formData?.submitField,
          res.formData?.model
        )
        config.state.model.id = id
        function mounted() {
          this.utils.on('out-submit', () => {
            this.methods.submit()
          })
        }
        function destroyed() {
          this.utils.off('out-submit')
        }
        config.methods.mounted = {
          type: JSFunction,
          value: mounted.toString(),
        }
        config.methods.destroyed = {
          type: JSFunction,
          value: destroyed.toString(),
        }
        config.state.status = FormState.READONLY
        // 默认隐藏提交按钮
        config.state.showSubmitBtn = false
        config.state.id = this.$route.query.id
        this.config = config
      } finally {
        this.loading = false
      }
    },
    handleEdit() {
      this.config.state.status = FormState.NORMAL
      this.isEdit = true
    },
    handleCancel() {
      this.config.state.status = FormState.READONLY
      this.isEdit = false
    },
    handleSave() {
      mitter.emit('out-submit')

      mitter.once('submit', async (data) => {
        if (window.parent === window) {
          // 如果是iframe，则不进行提示
          this.$message.success('保存成功')
        }

        this.config.state.status = FormState.READONLY
        this.isEdit = false
        const res = await formDataService.dataView({
          id: this.$route.query.id,
          formcode: this.$route.params.formcode,
        })
        this.config.state.model = submitFieldToModel(
          this.config.children[0].children,
          res.formData?.submitField,
          res.formData?.model
        )
      })
    },
    handleDelete() {
      deleteConfirm(
        this.$createElement,
        this.$route.query.id,
        this.$route.params.formcode
      ).then((res) => {
        // 当前不是iframe时
        if (res && window.parent === window) {
          // 获取默认的表单数据 进行跳转
          formDataService
            .getFirstData(this.$route.params.formcode)
            .then((res) => {
              if (!res) {
                location.replace(getUrl('/status/no-data'))
                return
              }
              this.$router.replace({
                path: this.$route.path,
                query: { id: res.id },
              })
              location.reload()
            })
        }
      })
    },
  },
}
</script>
