<template>
  <div class="submit-success">
    <div class="submit-content">
      <div class="icon-container">
        <i class="el-icon-check"></i>
      </div>
      <div class="text-primary mt-4 text-lg">提交成功</div>
      <div class="text-secondary mt-2">数据已提交并保存</div>
      <div class="button-group mt-6">
        <el-button type="primary" @click="continueSubmit">继续提交</el-button>
        <el-button @click="viewDetails">查看详情</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ZDSubmitSuccess',
  methods: {
    continueSubmit() {
      // 清空表单，准备继续提交
      this.$emit('continue-submit')
    },
    viewDetails() {
      // 查看提交详情
      this.$emit('view-details')
    },
  },
}
</script>

<style scoped>
.submit-success {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 300px;
}

.submit-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background-color: #0091ff;
  border-radius: 50%;
}

.icon-container i {
  color: #fff;
  font-size: 30px;
}

.text-primary {
  color: #303133;
  font-weight: 500;
  font-size: 18px;
}

.text-secondary {
  color: #909399;
  font-size: 14px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.mt-6 {
  margin-top: 24px;
}

.button-group {
  display: flex;
  gap: 10px;
}
</style>
