// 将表单字段数据转成model对象

const getValue = (fields, map) => {
  if (fields?.children?.length && map?.value?.length) {
    const arr = []
    const { labelMap, refMap } = map
    if (!labelMap || !refMap) {
      return []
    }
    const componentMap = {}
    fields.children.forEach((field) => {
      componentMap[field.ref] = field
    })
    // 有子组件时，对数据进行处理
    map.value.forEach((item) => {
      const v = {}
      Object.keys(item).forEach((key) => {
        const field = componentMap[key]
        if (!field?.props?.label) return
        const label = labelMap?.[field?.props?.label]
        if (refMap[key]) {
          v[field.ref] = item[field.ref]
        } else if (label && field.componentName === label.componentName) {
          v[field.ref] = item[label]
          // 命中后删除label 防止多个重名 重复命中
          delete labelMap[label.label]
        } else {
          v[field.ref] = null
        }
      })
      arr.push(v)
    })
    return arr
  }
  return map.value
}

export function submitFieldToModel(children, submitFields, defaultValue = {}) {
  if (!children?.length || !submitFields?.length) {
    return defaultValue
  }
  const model = {}
  const labelMap = {}
  const refMap = {}
  submitFields.forEach((item) => {
    if (item.children?.length) {
      const v = {}
      const v2 = []
      item.children.forEach((child) => {
        v[child.label] = child
        v2[child.ref] = child
      })
      labelMap[item.label] = {
        ...item,
        labelMap: v,
        refMap: v2,
      }
      refMap[item.ref] = {
        ...item,
        labelMap: v,
        refMap: v2,
      }
    } else {
      labelMap[item.label] = item
      refMap[item.ref] = item
    }
  })
  children.forEach((field) => {
    if (!field?.props?.label) return
    // 先命中ref map  没有就命中label map， 切换组件后不命中label
    const label = labelMap?.[field?.props?.label]
    if (refMap[field.ref]) {
      model[field.ref] = getValue(field, refMap[field.ref])
    } else if (label && field.componentName === label.componentName) {
      model[field.ref] = getValue(field, label)
      // 命中后删除label 防止多个重名 重复命中
      delete labelMap[label.label]
    } else {
      model[field.ref] = null
    }
  })

  return model
}
