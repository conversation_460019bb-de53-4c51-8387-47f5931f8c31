import { http } from '@/utils/http'
import { GroupAPI } from '@/api'
import { createLinkData } from '@/utils/createLinkData'

export let _list = createLinkData('group_tree_list', null)
/**
 * 分组相关服务，所有方法与 GroupAPI key 一一对应
 */
export const groupService = {
  /**
   * 获取应用分组树数据
   * @param {Object} appData 应用参数
   * @returns {Promise<Object>} 分组树数据
   */
  fetchAppTreeData(appData) {
    if (_list.value) {
      return Promise.resolve(JSON.parse(JSON.stringify(_list.value)))
    }
    return new Promise((resolve, reject) => {
      http
        .get(GroupAPI.GET_PAGE_LIST(), appData)
        .then((res) => {
          _list.value = JSON.parse(JSON.stringify(res.data))
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  /**
   * 保存分组
   * @param {Object} groupData 分组数据
   * @returns {Promise<Object>} 保存结果
   */
  savePageGroup(groupData) {
    return new Promise((resolve, reject) => {
      http
        .post(GroupAPI.SAVE_PAGE_GROUP(), groupData)
        .then((res) => {
          if (groupData.parentId === 0) {
            _list.value.unshift(res.data)
          } else {
            saveGroupData(_list.value, groupData.parentId, res.data)
          }
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  /**
   * 获取分组选项
   * @param {string} appcode 应用编码
   * @returns {Promise<Object>} 分组选项
   */
  fetchPageGroupList(appcode) {
    return new Promise((resolve, reject) => {
      http
        .get(GroupAPI.GET_PAGE_GROUP_LIST(), { appcode: appcode })
        .then((res) => {
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  /**
   * 获取分组树
   * @param {string} appcode 应用编码
   * @returns {Promise<Object>} 分组树
   */
  fetchPageGroupTree(appcode) {
    return new Promise((resolve, reject) => {
      http
        .get(GroupAPI.GET_PAGE_GROUP_TREE(), { appcode: appcode })
        .then((res) => {
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  /**
   * 修改分组名称
   * @param {Object} groupData 分组数据
   * @returns {Promise<Object>} 修改结果
   */
  updatePageName(groupData) {
    return new Promise((resolve, reject) => {
      http
        .post(GroupAPI.UPDATE_PAGE_NAME(), groupData)
        .then((res) => {
          updateGroupName(_list.value, groupData.code, groupData.name)
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  /**
   * 删除分组
   * @param {Object} groupData 分组数据
   * @returns {Promise<Object>} 删除结果
   */
  deletePage(groupData) {
    return new Promise((resolve, reject) => {
      http
        .post(GroupAPI.DELETE_PAGE(), groupData)
        .then((res) => {
          removeByCode(_list.value, groupData.code)
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  /**
   * 移动分组
   * @param {Object} moveData 移动参数
   * @returns {Promise<Object>} 移动结果
   */
  movePage(moveData) {
    return new Promise((resolve, reject) => {
      http
        .get(GroupAPI.MOVE_PAGE(), moveData)
        .then((res) => {
          _list.value = res.data
          resolve(res.data)
        })
        .catch((error) => {
          _list.value = null
          reject(error)
        })
    })
  },
}

function removeByCode(arr, targetCode) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].code === targetCode) {
      arr.splice(i, 1)
      return true
    }
    if (arr[i].children && arr[i].children.length) {
      removeByCode(arr[i].children, targetCode)
    }
  }
}

function saveGroupData(treeData, parentId, groupItemData) {
  for (let i = 0; i < treeData.length; i++) {
    if (treeData[i].id === parentId) {
      treeData[i].children = treeData[i].children || []
      treeData[i].children.push(groupItemData)
      return true
    }
    if (
      treeData[i].children &&
      saveGroupData(treeData[i].children, parentId, groupItemData)
    ) {
      return true
    }
  }
  return false
}

function updateGroupName(arr, targetCode, newName) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].code === targetCode) {
      arr[i].name = newName
      return true
    }
    if (arr[i].children) {
      if (updateGroupName(arr[i].children, targetCode, newName)) {
        return true
      }
    }
  }
  return false
}
