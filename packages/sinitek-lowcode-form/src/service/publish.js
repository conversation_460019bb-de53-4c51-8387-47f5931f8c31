import { http } from '@/utils/http'
import { PublishAPI } from '@/api'

export const publishService = {
  publishScene(publishData) {
    return new Promise((resolve, reject) => {
      http
        .post(PublishAPI.PUBLISH_FORM(), publishData)
        .then((res) => {
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  getPublishedUrl(formcode) {
    return new Promise((resolve, reject) => {
      http
        .get(PublishAPI.GET_FORM_URL(), { formcode: formcode })
        .then((res) => {
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
}
