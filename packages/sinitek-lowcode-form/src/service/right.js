import { http } from '@/utils/http'
import { RightAPI } from '@/api'

/**
 * 权限相关服务，所有方法与 RightAPI key 一一对应
 */
export const rightService = {
  /**
   * 获取页面权限列表
   * @param {Object} queryData 查询参数
   * @returns {Promise<Object>} 权限列表数据
   */
  fetchPageAuthList(queryData) {
    return new Promise((resolve, reject) => {
      http
        .post(RightAPI.GET_PAGE_AUTH_LIST(), queryData)
        .then((res) => {
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  /**
   * 保存或更新页面权限
   * @param {Object} authData 权限数据
   * @returns {Promise<Object>} 保存结果
   */
  saveOrUpdatePageAuth(authData) {
    return new Promise((resolve, reject) => {
      http
        .post(RightAPI.SAVE_OR_UPDATE_PAGE_AUTH(), authData)
        .then((res) => {
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
}
