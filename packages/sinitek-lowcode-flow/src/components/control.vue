<template>
  <div absolute right-0 top-0 flex gap-4 b rounded bg-white px-2 py-1 shadow>
    <div
      v-for="item of list"
      :key="item.key"
      flex="~ col justify-center items-center"
      cursor-pointer
      bg="hover:gray-100"
      fs-14
      p="x-2 t-1"
      :class="{
        'history-disabled': history[item.key + 'Able'] === false,
      }"
      @click="item.onClick"
    >
      <div :class="item.icon" size-6 bg-cover></div>
      <div fs-12>{{ item.text }}</div>
    </div>
    <el-dialog
      append-to-body
      title="查看数据"
      :visible.sync="dialogVisible"
      width="30%"
      click-outside
    >
      <div relative>
        <vue-json-pretty :height="500" :data="graphData" virtual show-icon />
        <div
          absolute
          right-5
          top-0
          cursor-pointer
          float="复制"
          title="复制"
          @click="copy"
        >
          <i class="el-icon-copy-document" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import { layout } from '../util/layout.js'
export default {
  name: 'FlowControl',
  components: {
    VueJsonPretty,
  },
  inject: ['flow'],
  data() {
    return {
      graphData: {},
      list: [
        {
          key: 'getData',
          iconClass: 'lf-control-fit',
          icon: 'i-material-symbols-light:data-object',
          title: '查看数据',
          text: '查看数据',
          onClick: () => {
            this.graphData = this.flow.lf.getGraphData()
            this.dialogVisible = true
          },
        },
        {
          key: 'pretty',
          iconClass: 'lf-control-fit',
          icon: 'i-material-symbols-light:hdr-auto-outline',
          title: '自动布局',
          text: '自动布局',
          onClick: async () => {
            // this.graphData = this.flow.lf.graphModel()
            // this.dialogVisible = true
            layout(this.flow.lf)
          },
        },
        {
          key: 'reset',
          iconClass: 'lf-control-fit',
          icon: 'i-material-symbols-light:fit-screen-outline',
          title: '恢复流程原有尺寸',
          text: '适应',
          onClick: () => {
            this.flow.lf.resetZoom()
          },
        },
        {
          key: 'undo',
          iconClass: 'lf-control-undo',
          icon: 'i-material-symbols-light:undo-rounded',
          title: '回到上一步',
          text: '上一步',
          onClick: () => {
            this.flow.lf.undo()
          },
        },
        {
          key: 'redo',
          iconClass: 'lf-control-redo',
          icon: 'i-material-symbols-light:redo-rounded',
          title: '移到下一步',
          text: '下一步',
          onClick: () => {
            this.flow.lf.redo()
          },
        },
      ],
      history: {
        undoAble: false,
        redoAble: false,
      },
      dialogVisible: false,
    }
  },
  mounted() {
    setTimeout(() => {
      this.flow.lf.on('history:change', ({ data: { undoAble, redoAble } }) => {
        this.history.undoAble = undoAble
        this.history.redoAble = redoAble
      })
    })
  },
  methods: {
    copy() {
      const text = JSON.stringify(this.graphData)
      try {
        if (navigator.clipboard) {
          // clipboard api 复制
          navigator.clipboard.writeText(text)
        } else {
          var textarea = document.createElement('textarea')
          document.body.appendChild(textarea)
          // 隐藏此输入框
          textarea.style.position = 'fixed'
          textarea.style.clip = 'rect(0 0 0 0)'
          textarea.style.top = '10px'
          // 赋值
          textarea.value = text
          // 选中
          textarea.select()
          // 复制
          document.execCommand('copy', true)
          // 移除输入框
          document.body.removeChild(textarea)
        }
        this.$message.success('复制成功')
      } catch (_) {
        this.$message.error('复制失败')
      }
    },
  },
}
</script>
<style scoped lang="scss">
.history-disabled {
  opacity: 0.5;
  pointer-events: none;
}
</style>
