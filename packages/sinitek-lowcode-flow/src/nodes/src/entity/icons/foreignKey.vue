<template>
  <svg
    t="1732087656493"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    :width="size"
    :height="size"
  >
    <path
      d="M659.503 265.944c-41.558-23.984-94.703-9.74-118.703 31.83s-9.768 94.723 31.79 118.707c41.55 23.999 94.692 9.743 118.692-31.827s9.771-94.711-31.779-118.71z m-52.005 304.01L430.022 877.35a30.488 30.488 0 0 1-20.833 14.726l-98.78 18.44c-14.79 2.761-29.382-5.673-34.39-19.854l-33.42-94.766a30.488 30.488 0 0 1 2.337-25.406l41.959-72.613 39.677 22.847 3.921 2.017c21.46 9.775 46.823 1.661 58.609-18.737l2.015-3.918c9.764-21.46 1.635-46.831-18.809-58.626l-39.68-22.856L422.4 463.069c-59.806-94.941-41.985-219.163 42.084-293.389 84.06-74.222 209.602-76.56 296.485-5.527 86.89 71.037 109.512 194.506 53.428 291.616-42.414 73.494-122.047 117.441-206.9 114.184z"
      fill="#27BA8F"
      p-id="1849"
    ></path>
  </svg>
</template>

<script>
export default {
  name: 'IconForeignKey',
  props: {
    size: {
      type: Number,
      default: 20,
    },
  },
}
</script>
