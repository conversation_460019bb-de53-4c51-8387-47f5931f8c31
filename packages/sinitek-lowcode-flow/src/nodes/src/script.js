import base from '../base'
import { Nodes } from '../const'
class ScriptNodeView extends base.view {}

class ScriptNodeModel extends base.model {
  validate() {
    const rules = super.validate()
    rules.push({
      message: '缺少函数名称',
      validate: () => {
        return !!this.properties.name
      },
    })
    return rules
  }
}

export default {
  type: Nodes.Script,
  model: ScriptNodeModel,
  view: ScriptNodeView,
}
