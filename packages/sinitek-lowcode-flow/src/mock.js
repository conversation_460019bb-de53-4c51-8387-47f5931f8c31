export const er = {
  nodes: [
    {
      id: 'test241129',
      type: 'entity-node',
      properties: {
        title: 'test241129',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 160,
        },
      },
    },
    {
      id: 'd-product',
      type: 'entity-node',
      properties: {
        title: 'product',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 160,
        },
      },
    },
    {
      id: 'workDTO',
      type: 'entity-node',
      properties: {
        title: '工作经历',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'resume_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'company_name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'position',
            type: 'SELECT',
            primary: false,
            foreign: false,
          },
          {
            key: 'start_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'end_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'description',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 340,
        },
      },
    },
    {
      id: 'resumeDTO',
      type: 'entity-node',
      properties: {
        title: '简历',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'candidate_name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'email',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'phone_number',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'address',
            type: 'TEXTAREA',
            primary: false,
            foreign: false,
          },
          {
            key: 'objective',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 310,
        },
      },
    },
    {
      id: 'test_lc_model',
      type: 'entity-node',
      properties: {
        title: 'test_lc_model',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'testenum',
            type: 'CHECKBOX_GROUP',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 250,
        },
      },
    },
    {
      id: 'test_lc_model2',
      type: 'entity-node',
      properties: {
        title: 'test_lc_model2',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'label',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test_lc_model3',
      type: 'entity-node',
      properties: {
        title: 'test_lc_model3',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'label',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test_lc_model4',
      type: 'entity-node',
      properties: {
        title: 'test_lc_model4',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'model5_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'label',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'orgid',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 280,
        },
      },
    },
    {
      id: 'test_lc_model5',
      type: 'entity-node',
      properties: {
        title: 'test_lc_model5',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'label',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'TEST_R_VALUE_SUB_MODEL_CHILD',
      type: 'entity-node',
      properties: {
        title: 'test_rela_value_sub_model_child',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'number_type',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_type',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 280,
        },
      },
    },
    {
      id: 'TEST_R_VALUE_SUB_MODEL_PARENT',
      type: 'entity-node',
      properties: {
        title: 'test_rela_value_sub_model_parent',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test_validate_unique',
      type: 'entity-node',
      properties: {
        title: '测试唯一校验',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'int_col',
            type: 'NUMBER',
            primary: false,
            foreign: false,
          },
          {
            key: 'long_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'bool_col',
            type: 'SWITCH',
            primary: false,
            foreign: false,
          },
          {
            key: 'decimal_col',
            type: 'RATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'foreign_key',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'date_col',
            type: 'DATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 460,
        },
      },
    },
    {
      id: 'test_multi_validate_unique',
      type: 'entity-node',
      properties: {
        title: '测试多值唯一校验',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'int_col',
            type: 'NUMBER',
            primary: false,
            foreign: false,
          },
          {
            key: 'long_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'bool_col',
            type: 'SWITCH',
            primary: false,
            foreign: false,
          },
          {
            key: 'decimal_col',
            type: 'RATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'foreign_key',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'date_col',
            type: 'DATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 460,
        },
      },
    },
    {
      id: 'test_foreign_key_child',
      type: 'entity-node',
      properties: {
        title: 'test_foreign_key_child',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'num',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 250,
        },
      },
    },
    {
      id: 'test_foreign_key_parent',
      type: 'entity-node',
      properties: {
        title: 'test_foreign_key_parent',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test-mv-child',
      type: 'entity-node',
      properties: {
        title: '测试多值(子)',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 280,
        },
      },
    },
    {
      id: 'test-mv-parent',
      type: 'entity-node',
      properties: {
        title: '测试多值(父)',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 250,
        },
      },
    },
    {
      id: 'test-enum-prop-mv-type-logic-d',
      type: 'entity-node',
      properties: {
        title: '测试枚举多值逻辑删除',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'remove_flag',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 250,
        },
      },
    },
    {
      id: 'TEST_SUB_MODEL_CHILD',
      type: 'entity-node',
      properties: {
        title: 'test_sub_model_child',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'TEST_SUB_MODEL_PARENT',
      type: 'entity-node',
      properties: {
        title: 'test_sub_model_parent',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test-model-export',
      type: 'entity-node',
      properties: {
        title: '测试模型导入导出',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 160,
        },
      },
    },
    {
      id: 'test-audit',
      type: 'entity-node',
      properties: {
        title: 'test-audit',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test_crud',
      type: 'entity-node',
      properties: {
        title: 'test_crud',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test_crud_or',
      type: 'entity-node',
      properties: {
        title: '测试操作人',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'remove_flag',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'creatorid',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updaterid',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'removerid',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 310,
        },
      },
    },
    {
      id: 'test_model_search',
      type: 'entity-node',
      properties: {
        title: 'test_model_search',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'test_code',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'test_name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'start_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'end_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 370,
        },
      },
    },
    {
      id: 'test_model_search_child',
      type: 'entity-node',
      properties: {
        title: 'test_model_search_child',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'test_code',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'test_name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'start_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'end_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 400,
        },
      },
    },
    {
      id: 'test_model_search_parent',
      type: 'entity-node',
      properties: {
        title: 'test_model_search_parent',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'test_code',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'test_name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'start_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'end_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 370,
        },
      },
    },
    {
      id: 'test_model_search_child2',
      type: 'entity-node',
      properties: {
        title: 'test_model_search_child2',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'test_code',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'test_name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'start_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'end_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 400,
        },
      },
    },
    {
      id: 'test_model_search_parent2',
      type: 'entity-node',
      properties: {
        title: 'test_model_search_parent2',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'test_code',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'test_name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'start_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'end_date',
            type: 'DATETIME',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 370,
        },
      },
    },
    {
      id: 'test_validate_all_common_prop',
      type: 'entity-node',
      properties: {
        title: '所有普通数据模型属性类型(校验用)',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'int_col',
            type: 'NUMBER',
            primary: false,
            foreign: false,
          },
          {
            key: 'long_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'bool_col',
            type: 'SWITCH',
            primary: false,
            foreign: false,
          },
          {
            key: 'decimal_col',
            type: 'RATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'foreign_key',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'date_col',
            type: 'DATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 460,
        },
      },
    },
    {
      id: 'test_string_validate',
      type: 'entity-node',
      properties: {
        title: '测试字符串校验',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test_validate_numprop',
      type: 'entity-node',
      properties: {
        title: '测试数字校验',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'int_col',
            type: 'NUMBER',
            primary: false,
            foreign: false,
          },
          {
            key: 'long_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'decimal_col',
            type: 'RATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 250,
        },
      },
    },
    {
      id: 'test_data_model_config',
      type: 'entity-node',
      properties: {
        title: 'test_data_model_config',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'a_name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'ALL_COMMON_PROP',
      type: 'entity-node',
      properties: {
        title: '所有普通数据模型属性类型',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'int_col',
            type: 'NUMBER',
            primary: false,
            foreign: false,
          },
          {
            key: 'long_col',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'bool_col',
            type: 'SWITCH',
            primary: false,
            foreign: false,
          },
          {
            key: 'decimal_col',
            type: 'RATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'foreign_key',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'date_col',
            type: 'DATE',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 460,
        },
      },
    },
    {
      id: 'test_custom_handler_show',
      type: 'entity-node',
      properties: {
        title: '测试自定义处理器展示',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'custom_prop',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'test_logic_remove',
      type: 'entity-node',
      properties: {
        title: '测试逻辑删除',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'remove_flag',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'test-enum-prop-mv-type',
      type: 'entity-node',
      properties: {
        title: '测试枚举多值',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'test-enum-prop-type',
      type: 'entity-node',
      properties: {
        title: '测试枚举属性类型',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'str_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'num_enum',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'test-tree',
      type: 'entity-node',
      properties: {
        title: 'test-tree',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'sort',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'high_val',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'code_val',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'name_val',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 340,
        },
      },
    },
    {
      id: 'p1',
      type: 'entity-node',
      properties: {
        title: 'test_p1',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'p2',
      type: 'entity-node',
      properties: {
        title: 'test_p2',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'p3',
      type: 'entity-node',
      properties: {
        title: 'test_p3',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'p4',
      type: 'entity-node',
      properties: {
        title: 'test_p4',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'test-sirmorg-prop-type-mv',
      type: 'entity-node',
      properties: {
        title: '测试组织结构多值',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test-sirmorg-mv-child',
      type: 'entity-node',
      properties: {
        title: '测试组织结构多值(子)',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'parent_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 220,
        },
      },
    },
    {
      id: 'test-sirmorg-mv-parent',
      type: 'entity-node',
      properties: {
        title: '测试组织结构多值(父)',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'test-sirmorg-prop-type',
      type: 'entity-node',
      properties: {
        title: '测试组织结构属性类型',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'org_id',
            type: 'SIRMORG',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'aaa',
      type: 'entity-node',
      properties: {
        title: 'aaa',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 160,
        },
      },
    },
    {
      id: 'test_attachment',
      type: 'entity-node',
      properties: {
        title: '测试附件',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'name',
            type: 'INPUT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 190,
        },
      },
    },
    {
      id: 'productDTO',
      type: 'entity-node',
      properties: {
        title: '产品DTO',
        fields: [
          {
            key: 'id',
            type: 'TEXT',
            primary: true,
            foreign: false,
          },
          {
            key: 'category',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'unit_price',
            type: 'MONEY',
            primary: false,
            foreign: false,
          },
          {
            key: 'stock_quantity',
            type: 'NUMBER',
            primary: false,
            foreign: false,
          },
          {
            key: 'supplier_id',
            type: 'RELA_FORM',
            primary: false,
            foreign: false,
          },
          {
            key: 'product_name',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: 'TEXT',
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 310,
        },
      },
    },
    {
      id: 'supplierDTO',
      type: 'entity-node',
      properties: {
        title: '供应商DTO',
        fields: [
          {
            key: 'id',
            type: null,
            primary: true,
            foreign: false,
          },
          {
            key: 'contact_person',
            type: null,
            primary: false,
            foreign: false,
          },
          {
            key: 'mobile',
            type: null,
            primary: false,
            foreign: false,
          },
          {
            key: 'supplier_name',
            type: null,
            primary: false,
            foreign: false,
          },
          {
            key: 'version',
            type: null,
            primary: false,
            foreign: false,
          },
          {
            key: 'createtimestamp',
            type: null,
            primary: false,
            foreign: false,
          },
          {
            key: 'updatetimestamp',
            type: null,
            primary: false,
            foreign: false,
          },
        ],
        size: {
          width: 250,
          height: 250,
        },
      },
    },
  ],
  edges: [
    {
      id: 'test241129-workDTO',
      sourceNodeId: 'test241129',
      targetNodeId: 'workDTO',
      text: '子模型',
      type: 'entity-line',
      properties: {
        source: 'o{',
      },
    },
    {
      id: 'workDTO-resumeDTO',
      sourceNodeId: 'workDTO',
      targetNodeId: 'resumeDTO',
      text: '关联:resumeId',
      type: 'entity-line',
      properties: {
        source: '}o',
        target: 'o{',
      },
    },
    {
      id: 'test_lc_model-test_lc_model2',
      sourceNodeId: 'test_lc_model',
      targetNodeId: 'test_lc_model2',
      text: '关联:parentId',
      type: 'entity-line',
      properties: {
        source: '}o',
      },
    },
    {
      id: 'test_lc_model3-test_lc_model4',
      sourceNodeId: 'test_lc_model3',
      targetNodeId: 'test_lc_model4',
      text: '子模型',
      type: 'entity-line',
      properties: {
        source: 'o{',
        target: '}o',
      },
    },
    {
      id: 'test_lc_model4-test_lc_model5',
      sourceNodeId: 'test_lc_model4',
      targetNodeId: 'test_lc_model5',
      text: '关联:model5Id',
      type: 'entity-line',
      properties: {
        source: '}o',
      },
    },
    {
      id: 'TEST_R_VALUE_SUB_MODEL_CHILD-TEST_SUB_MODEL_PARENT',
      sourceNodeId: 'TEST_R_VALUE_SUB_MODEL_CHILD',
      targetNodeId: 'TEST_SUB_MODEL_PARENT',
      text: '关联:parentId',
      type: 'entity-line',
      properties: {
        source: '}o',
      },
    },
    {
      id: 'TEST_R_VALUE_SUB_MODEL_PARENT-TEST_R_VALUE_SUB_MODEL_CHILD',
      sourceNodeId: 'TEST_R_VALUE_SUB_MODEL_PARENT',
      targetNodeId: 'TEST_R_VALUE_SUB_MODEL_CHILD',
      text: '子模型',
      type: 'entity-line',
      properties: {
        source: 'o{',
      },
    },
    {
      id: 'test_foreign_key_child-test_foreign_key_parent',
      sourceNodeId: 'test_foreign_key_child',
      targetNodeId: 'test_foreign_key_parent',
      text: '关联:parentId',
      type: 'entity-line',
      properties: {
        source: '}o',
      },
    },
    {
      id: 'test-mv-child-test-mv-parent',
      sourceNodeId: 'test-mv-child',
      targetNodeId: 'test-mv-parent',
      text: '关联:parentId',
      type: 'entity-line',
      properties: {
        source: '}o',
        target: 'o{',
      },
    },
    {
      id: 'TEST_SUB_MODEL_CHILD-TEST_SUB_MODEL_PARENT',
      sourceNodeId: 'TEST_SUB_MODEL_CHILD',
      targetNodeId: 'TEST_SUB_MODEL_PARENT',
      text: '关联:parentId',
      type: 'entity-line',
      properties: {
        source: '}o',
        target: 'o{',
      },
    },
    {
      id: 'test-model-export-test_lc_model',
      sourceNodeId: 'test-model-export',
      targetNodeId: 'test_lc_model',
      text: '子模型',
      type: 'entity-line',
      properties: {
        source: 'o{',
      },
    },
    {
      id: 'test_model_search_child-test_model_search_parent',
      sourceNodeId: 'test_model_search_child',
      targetNodeId: 'test_model_search_parent',
      text: '关联:parentId',
      type: 'entity-line',
      properties: {
        source: '}o',
        target: 'o{',
      },
    },
    {
      id: 'test_model_search_child2-test_model_search_parent2',
      sourceNodeId: 'test_model_search_child2',
      targetNodeId: 'test_model_search_parent2',
      text: '关联:parentId',
      type: 'entity-line',
      properties: {
        source: '}o',
        target: 'o{',
      },
    },
    {
      id: 'p1-p2',
      sourceNodeId: 'p1',
      targetNodeId: 'p2',
      text: '子模型',
      type: 'entity-line',
      properties: {
        source: 'o{',
        target: '}o',
      },
    },
    {
      id: 'p2-p3',
      sourceNodeId: 'p2',
      targetNodeId: 'p3',
      text: '子模型',
      type: 'entity-line',
      properties: {
        source: 'o{',
        target: '}o',
      },
    },
    {
      id: 'p3-p4',
      sourceNodeId: 'p3',
      targetNodeId: 'p4',
      text: '子模型',
      type: 'entity-line',
      properties: {
        source: 'o{',
        target: '}o',
      },
    },
    {
      id: 'test-sirmorg-mv-child-test-sirmorg-mv-parent',
      sourceNodeId: 'test-sirmorg-mv-child',
      targetNodeId: 'test-sirmorg-mv-parent',
      text: '关联:parentId',
      type: 'entity-line',
      properties: {
        source: '}o',
        target: 'o{',
      },
    },
    {
      id: 'productDTO-supplierDTO',
      sourceNodeId: 'productDTO',
      targetNodeId: 'supplierDTO',
      text: '关联:supplierId',
      type: 'entity-line',
      properties: {
        source: '}o',
      },
    },
  ],
}
