<template>
  <ExpressionEditor
    v-model="currentValue"
    placeholder="表达式, 字符串需要使用引号包裹"
    w-full
    @input="onInput"
    @clear="$emit('clear')"
  />
</template>

<script>
import { ExpressionEditor } from 'sinitek-lowcode-shared'
export default {
  name: 'SettingInput',
  components: {
    ExpressionEditor,
  },
  inject: ['flow'],
  props: {
    value: String,
  },
  data() {
    return {
      currentValue: this.value,
    }
  },
  watch: {
    value: {
      handler(v) {
        this.currentValue = v
      },
    },
  },
  methods: {
    onInput(v) {
      this.$emit('input', v)
    },
  },
}
</script>
