<template>
  <Panel group="选项">
    <Item label="数据源">
      <el-select v-model="ds" size="mini" clearable w-full @change="onChange">
        <el-option
          v-for="item of dsList"
          :key="item.id"
          :label="item.id"
          :value="item.id"
          >{{ item.id }}</el-option
        >
      </el-select>
    </Item>
    <Item v-if="showType" label="类型">
      <el-select
        v-model="type"
        size="mini"
        clearable
        w-full
        @change="onTypeChange"
      >
        <el-option
          v-for="item of list"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          >{{ item.label }}</el-option
        >
      </el-select>
    </Item>
    <Item v-if="showParam" label="参数">
      <div flex="~ col gap-2">
        <XParam
          v-for="(_, i) of params.length + 1"
          :key="i"
          :data="params[i] || {}"
          :index="i"
          @changeKey="changeKey"
          @changeValue="changeValue"
          @clear="clear"
        />
      </div>
    </Item>
  </Panel>
</template>

<script>
import Panel from '../components/panel.vue'
import Item from '../components/item.vue'
import XParam from './param.vue'
import { setProperties, updateText } from '../../../util/api.js'
export default {
  name: 'SettingDatasource',
  components: {
    Item,
    Panel,
    XParam,
  },
  inject: ['flow'],
  props: {
    data: Object,
  },
  data() {
    return {
      ds: this.data.properties?.datasourceID || '',
      dsList: [],
      list: [
        { label: '列表', value: 'list' },
        { label: '懒加载列表', value: 'lazyList' },
        { label: '单条', value: 'single' },
        { label: '懒加载单条', value: 'lazySingle' },
        { label: '创建', value: 'create' },
        { label: '更新', value: 'update' },
        { label: '删除', value: 'delete' },
      ],
      type: this.data.properties?.type || 'list',
      params: this.data.properties?.params || [],
    }
  },
  computed: {
    showType() {
      // const schema = this.$doc.getSchema()
      const item = this.dsList.find((e) => e.id === this.ds)
      return item && item.type === 'model'
    },
    showParam() {
      // const schema = this.$doc.getSchema()
      const item = this.dsList.find((e) => e.id === this.ds)
      return (
        item &&
        ((item.type === 'model' && this.type !== 'list') ||
          item.type !== 'enum')
      )
    },
  },
  watch: {
    params: {
      deep: true,
      handler(v) {
        setProperties(this.flow.lf, this.data.id, {
          params: v,
        })
      },
    },
  },
  mounted() {
    this.dsList = this.flow.config.getDatasource()
  },
  methods: {
    onChange(v) {
      setProperties(this.flow.lf, this.data.id, {
        datasourceID: v,
      })
      updateText(this.flow.lf, this.data.id, v, true)
    },
    onTypeChange(v) {
      setProperties(this.flow.lf, this.data.id, {
        type: v,
      })
    },
    changeKey(i, v) {
      this.params.splice(i, 1, { ...this.params[i], key: v })
    },
    changeValue(i, v) {
      this.params.splice(i, 1, { ...this.params[i], value: v })
    },
    clear(i, v) {
      if (v) {
        this.params.splice(i, 1)
      }
    },
  },
}
</script>
