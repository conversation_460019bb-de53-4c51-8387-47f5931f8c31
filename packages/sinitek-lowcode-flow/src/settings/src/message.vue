<template>
  <Panel group="选项">
    <Item label="内容">
      <SettingInput v-model="message" @input="onInput" />
    </Item>
    <Item label="类型">
      <el-select v-model="type" size="mini" clearable w-full @change="onChange">
        <el-option
          v-for="item of list"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          >{{ item.label }}</el-option
        >
      </el-select>
    </Item>
  </Panel>
</template>

<script>
import Panel from './components/panel.vue'
import Item from './components/item.vue'
import { setProperties, updateText } from '../../util/api.js'
import SettingInput from './components/setting-input.vue'
export default {
  name: 'SettingMessage',
  components: {
    Item,
    Panel,
    SettingInput,
  },
  inject: ['flow'],
  props: {
    data: Object,
  },
  data() {
    return {
      message: this.data.properties.message,
      type: this.data.properties.type || 'info',
      list: [
        { label: '默认', value: 'info' },
        { label: '警告', value: 'warning' },
        { label: '错误', value: 'error' },
        { label: '成功', value: 'success' },
      ],
    }
  },
  methods: {
    onChange(v) {
      setProperties(this.flow.lf, this.data.id, {
        type: v,
      })
    },
    onInput(v) {
      setProperties(this.flow.lf, this.data.id, {
        message: v,
      })
      updateText(this.flow.lf, this.data.id, v, true)
    },
  },
}
</script>
