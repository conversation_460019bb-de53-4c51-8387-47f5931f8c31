export const NODE_SPACE_X = 160 // 节点横向距离
export const NODE_SPACE_Y = 80 // 节点纵向距离
export const PATH_SPACE_Y = 60 // 路径纵向距离
export const GRAPH_START_X = 100 // 图形默认起点X
export const GRAPH_START_Y = 100 // 图形默认起点Y
export const NODE_WIDTH = 100 // 节点宽度
export const NODE_HEIGHT = 28 // 节点高度
export const LINE_OFFSET = 24 // 节点高度

export const EDITOR_EVENT = {
  ONLOAD: 'onload',
  ERROR: 'error',
  RESET: 'reset',
  PREVIEW: 'preview',
  CANVAS_MODEL_ACTIVATED: 'canvas:model-activated',
  CANVAS_MODEL_DELETED: 'canvas:model-deleted',
  CANVAS_MODEL_HOVERED: 'canvas:model-hovered',
  CANVAS_MODEL_CLICKED: 'canvas:model-clicked',
  LOGIC_NODE_ADD: 'logic:node-add',
  LOGIC_NODE_HOVER: 'logic:node-hover',
  LOGIC_NODE_DYN_DATA: 'logic:node-dyn-data',
  LOGIC_HISTORY_CHANGE: 'logic:history-change',
}
