import defineConfig from '../../config/rspack/base.mjs'

import path from 'path'
function resolve(dir) {
  return path.resolve(dir)
}

export default defineConfig({
  context: resolve('./'),
  unoConfigPath: resolve('./uno.config.js'),
  library: {
    type: 'umd',
    name: 'sinitek-lowcode-flow',
    entry: './index.js',
  },
  // transpileDependencies: ['sinitek-lowcode-shared'],
  css: {
    extract:
      process.env.NODE_ENV === 'development'
        ? {
            filename: '[name].css',
            chunkFilename: 'css/[name].css',
          }
        : false,
  },
  devServer: {
    port: '8991',
    client: {
      progress: true,
      overlay: {
        warnings: false,
        runtimeErrors: (error) => {
          if (
            error.message ===
            'ResizeObserver loop completed with undelivered notifications.'
          ) {
            return false
          }
          return true
        },
      },
    },
  },
  assetsDir: 'static', // 静态资源目录名称
  productionSourceMap: process.env.NODE_ENV === 'development', // 去掉打包的时候生成的map文件

  chainWebpack: (config) => {
    config.resolve.alias.set(
      '@logicflow/extension$',
      resolve('./node_modules/@logicflow/extension/dist/index.min.js')
    )
    // 配置别名
    if (process.env.NODE_ENV === 'production') {
      // 打包时排除
      config.externals({
        vue: 'vue',
        'core-js': 'core-js',
        'element-ui': 'element-ui',
        'monaco-editor': 'monaco-editor',
        'sinitek-ui': 'sinitek-ui',
        '@iconify/vue2': '@iconify/vue2',
        sirmapp: 'sirmapp',
        'sinitek-lowcode-advanced': 'sinitek-lowcode-advanced',
        'sinitek-lowcode-materials': 'sinitek-lowcode-materials',
        'sinitek-lowcode-render': 'sinitek-lowcode-render',
        'sinitek-lowcode-shared': 'sinitek-lowcode-shared',
      })
    } else {
      /* 为开发环境修改配置... */
      // config.module.rule('vue').uses.delete('cache-loader')
      // config.merge({
      //   cache: false,
      // })
      config.entryPoints
        .clear()
        .end()
        .entry('main')
        .add(resolve('./example/main.js'))

      // 修改 webpack-html-plugin 配置
      config.plugin('html').tap(() => {
        return [
          // 传递给 html-webpack-plugin 构造函数的新参数
          {
            template: './example/index.html',
          },
        ]
      })
    }
  },
})
